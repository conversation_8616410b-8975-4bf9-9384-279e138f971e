{"name": "onRoad-App", "version": "1.0.0", "description": "React Native application for onRoad", "main": "App.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "npx react-native start", "android": "npx react-native run-android", "ios": "npx react-native run-ios"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "babel-jest": "30.0.0-beta.3", "jest": "^29.7.0", "jest-environment-jsdom": "30.0.0-beta.3", "metro-react-native-babel-preset": "^0.77.0", "react-test-renderer": "^19.1.0"}, "dependencies": {"formik": "^2.4.6", "react-native-web": "^0.20.0", "yup": "^1.6.1"}}