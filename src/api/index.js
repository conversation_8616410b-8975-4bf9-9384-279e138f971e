/**
 * OnRoad API Service
 * Based on the OpenAPI 3.0 specification for OnRoad Real Estate API
 */

const API_BASE_URL = 'https://your-express-api.example.com'; // TODO: Replace with actual API URL

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = null;
  }

  /**
   * Set authentication token
   * @param {string} token - JWT token from Supabase
   */
  setAuthToken(token) {
    this.token = token;
  }

  /**
   * Get authentication headers
   * @returns {Object} Headers object with authorization
   */
  getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make authenticated API request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Fetch options
   * @returns {Promise} API response
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getAuthHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Authentication Methods

  /**
   * Execute first sign-in logic for a new user
   * @returns {Promise} First sign-in response
   */
  async firstSignIn() {
    return this.makeRequest('/firstSignin', {
      method: 'POST',
    });
  }

  // Profile Methods

  /**
   * Create a new user profile
   * @param {Object} profileData - Profile information
   * @returns {Promise} Created profile
   */
  async createProfile(profileData) {
    return this.makeRequest('/profiles', {
      method: 'POST',
      body: JSON.stringify(profileData),
    });
  }

  // Deal Methods

  /**
   * Get all deals for the authenticated user
   * @returns {Promise} Array of deals
   */
  async getDeals() {
    return this.makeRequest('/deals');
  }

  /**
   * Create a new deal
   * @param {Object} dealData - Deal information
   * @returns {Promise} Created deal
   */
  async createDeal(dealData) {
    return this.makeRequest('/deals', {
      method: 'POST',
      body: JSON.stringify(dealData),
    });
  }

  /**
   * Get a specific deal by ID
   * @param {string} dealId - Deal ID
   * @returns {Promise} Deal details
   */
  async getDeal(dealId) {
    return this.makeRequest(`/deals/${dealId}`);
  }

  // Property Methods

  /**
   * Get properties for a deal
   * @param {string} dealId - Deal ID
   * @returns {Promise} Array of properties
   */
  async getProperties(dealId) {
    return this.makeRequest(`/dealOptions/${dealId}/properties`);
  }

  /**
   * Add a new property to a deal
   * @param {string} dealId - Deal ID
   * @param {Object} propertyData - Property information
   * @returns {Promise} Created property
   */
  async createProperty(dealId, propertyData) {
    return this.makeRequest(`/dealOptions/${dealId}/properties`, {
      method: 'POST',
      body: JSON.stringify(propertyData),
    });
  }

  /**
   * Update a property
   * @param {string} propertyId - Property ID
   * @param {Object} propertyData - Updated property information
   * @returns {Promise} Updated property
   */
  async updateProperty(propertyId, propertyData) {
    return this.makeRequest(`/dealOptions/properties/${propertyId}`, {
      method: 'PUT',
      body: JSON.stringify(propertyData),
    });
  }

  /**
   * Delete a property
   * @param {string} propertyId - Property ID
   * @returns {Promise} Deletion confirmation
   */
  async deleteProperty(propertyId) {
    return this.makeRequest(`/dealOptions/properties/${propertyId}`, {
      method: 'DELETE',
    });
  }

  // Form Methods

  /**
   * Get forms for a property
   * @param {string} propertyId - Property ID
   * @returns {Promise} Array of forms
   */
  async getForms(propertyId) {
    return this.makeRequest(`/forms/property/${propertyId}`);
  }

  /**
   * Add a new form to a property
   * @param {string} propertyId - Property ID
   * @param {Object} formData - Form information
   * @returns {Promise} Created form
   */
  async createForm(propertyId, formData) {
    return this.makeRequest(`/forms/property/${propertyId}`, {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  }

  /**
   * Update a form
   * @param {string} formId - Form ID
   * @param {Object} formData - Updated form information
   * @returns {Promise} Updated form
   */
  async updateForm(formId, formData) {
    return this.makeRequest(`/forms/${formId}`, {
      method: 'PUT',
      body: JSON.stringify(formData),
    });
  }

  /**
   * Delete a form
   * @param {string} formId - Form ID
   * @returns {Promise} Deletion confirmation
   */
  async deleteForm(formId) {
    return this.makeRequest(`/forms/${formId}`, {
      method: 'DELETE',
    });
  }

  // Client Intake Methods

  /**
   * Get realtor intake schema for a client
   * @param {string} searchId - Search ID
   * @returns {Promise} Intake schema
   */
  async getClientIntakeSchema(searchId) {
    return this.makeRequest(`/client/intake/schema/${searchId}`);
  }

  /**
   * Get client intake form responses
   * @param {string} searchId - Search ID
   * @returns {Promise} Intake form responses
   */
  async getClientIntakeAnswers(searchId) {
    return this.makeRequest(`/client/intake/answers/get/${searchId}`);
  }

  /**
   * Get client intake schema by deal ID
   * @param {string} dealId - Deal ID
   * @returns {Promise} Intake schema
   */
  async getRealtorClientIntake(dealId) {
    return this.makeRequest(`/realtor/client-intake/get-by-dealid/${dealId}`);
  }

  /**
   * Publish client intake template to tether
   * @param {string} dealId - Deal ID
   * @returns {Promise} Publish confirmation
   */
  async publishIntakeTemplate(dealId) {
    return this.makeRequest(`/realtor/client-intake/push-schema-to-tether/${dealId}`, {
      method: 'PATCH',
    });
  }
}

// Create and export a singleton instance
const apiService = new ApiService();

export default apiService;
