import React from 'react';
import { render } from '@testing-library/react-native';
import LoginScreen from '../LoginScreen';

// Mock navigation prop
const mockNavigate = jest.fn();
const navigation = { navigate: mockNavigate };

describe('LoginScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all input fields and buttons', () => {
    const { toJSON } = render(<LoginScreen navigation={navigation} />);
    const tree = toJSON();

    // Check that the component renders without crashing
    expect(tree).toBeTruthy();

    // Convert to string to check for content
    const treeString = JSON.stringify(tree);
    expect(treeString).toContain('OnRoad');
    expect(treeString).toContain('Welcome Back');
    expect(treeString).toContain('Sign In');
    expect(treeString).toContain('Email');
    expect(treeString).toContain('Password');
    expect(treeString).toContain('Forgot Password?');
    expect(treeString).toContain('Sign Up');
  });

  it('renders without crashing when form is submitted', () => {
    const { toJSON } = render(<LoginScreen navigation={navigation} />);
    const tree = toJSON();

    // Check that the component renders without crashing
    expect(tree).toBeTruthy();

    // This is a basic smoke test - the component should render
    // More complex interaction testing would require better setup
  });

  it('contains form validation schema', () => {
    const { toJSON } = render(<LoginScreen navigation={navigation} />);
    const tree = toJSON();

    // Check that the component renders the form structure
    expect(tree).toBeTruthy();

    // The component uses Formik and Yup for validation
    // This test ensures the component doesn't crash when rendering
  });

  it('has navigation functionality', () => {
    const { toJSON } = render(<LoginScreen navigation={navigation} />);
    const tree = toJSON();

    // Check that the component renders with navigation prop
    expect(tree).toBeTruthy();
    expect(navigation).toBeDefined();
    expect(navigation.navigate).toBeDefined();
  });
});
