import '@testing-library/jest-native/extend-expect';

// <PERSON>ck React Native modules
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native-web');

  // Add any React Native specific mocks here
  RN.StyleSheet = {
    create: (styles) => styles,
    flatten: (style) => {
      if (Array.isArray(style)) {
        return style.reduce((acc, s) => ({ ...acc, ...RN.StyleSheet.flatten(s) }), {});
      }
      return style || {};
    },
  };

  return RN;
});

// Setup global test environment
global.__DEV__ = true;
