## [9.3.1] 2021-11-19

### Changed

- Updated package.json metadata.

## [9.3.0] 2021-11-17

### Changed

- Convert the macOS applet to be universal so that it can run without Rosetta 2 installed.

- Add instructions how to update the macOS applet that is included in this library.

## [9.2.0] 2020-04-29

### Fixed

- Update TypeScript types to accommodate recent changes, see
[#117](https://github.com/jorangreef/sudo-prompt/issues/117).

## [9.1.0] 2019-11-13

### Added

- Add TypeScript types.

## [9.0.0] 2019-06-03

### Changed

- Make cross-platform `stdout`, `stderr` behavior consistent, see
[#89](https://github.com/jorangreef/sudo-prompt/issues/89).

- Preserve current working directory on all platforms.

- Improve kdesudo dialog appearance.

### Added

- Add `options.env` to set environment variables on all platforms, see
[#91](https://github.com/jorangreef/sudo-prompt/issues/91).

### Fixed

- Always return PERMISSION_DENIED as an Error object.

- Support multiple commands separated by semicolons on Linux, see
[#39](https://github.com/jorangreef/sudo-prompt/issues/39).

- Distinguish between elevation errors and command errors on Linux, see
[#88](https://github.com/jorangreef/sudo-prompt/issues/88).

- Fix Windows to return `PERMISSION_DENIED` Error even when Windows' error
messages are internationalized, see 
[#96](https://github.com/jorangreef/sudo-prompt/issues/96).

## [8.2.5] 2018-12-12

### Fixed

- Whitelist package.json files.

## [8.2.4] 2018-12-12

### Added

- A CHANGELOG.md file, see
[#78](https://github.com/jorangreef/sudo-prompt/issues/78).

## [8.2.3] 2018-09-11

### Fixed

- README: Link to concurrency discussion.

## [8.2.2] 2018-09-11

### Fixed

- README: Details on concurrency.

## [8.2.1] 2018-09-11

### Fixed

- A rare idempotency edge case where a command might have been run more than
once, given a very specific OS environment setup.

## [8.2.0] 2018-03-22

### Added

- Windows: Fix `cd` when `cwd` is on another drive, see
[#70](https://github.com/jorangreef/sudo-prompt/issues/70).

## [8.1.0] 2018-01-10

### Added

- Linux: Increase `maxBuffer` limit to 128 MiB, see
[#66](https://github.com/jorangreef/sudo-prompt/issues/66).

## [8.0.0] 2018-11-02

### Changed

- Windows: Set code page of command batch script to UTF-8.

## [7.1.1] 2017-07-18

### Fixed

- README: Explicitly mention that no child process is returned.

## [7.0.0] 2017-03-15

### Changed

- Add status code to errors on Windows and macOS.

## [6.2.1] 2016-12-16

### Fixed

- README: Syntax highlighting.

## [6.2.0] 2016-08-17

### Fixed

- README: Rename OS X to macOS.

## [6.1.0] 2016-08-02

### Added

- Yield an error if no polkit authentication agent is found, see
[#29](https://github.com/jorangreef/sudo-prompt/issues/29).

## [6.0.2] 2016-07-21

### Fixed

- README: Update explanation of Linux behavior.

## [6.0.1] 2016-07-15

### Fixed

- Update keywords in package.json.

## [6.0.0] 2016-07-15

### Changed

- Add support for Windows.
