{"version": 3, "names": ["proposalPlugins", "exports", "Set", "proposalSyntaxPlugins", "pluginSyntaxObject", "pluginSyntaxEntries", "Object", "keys", "map", "key", "pluginSyntaxMap", "Map"], "sources": ["../src/shipped-proposals.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n/* eslint sort-keys: \"error\" */\n// These mappings represent the transform plugins that have been\n// shipped by browsers, and are enabled by the `shippedProposals` option.\n\nconst proposalPlugins = new Set<string>([]);\n\n// proposal syntax plugins enabled by the `shippedProposals` option.\n// Unlike proposalPlugins above, they are independent of compiler targets.\nconst proposalSyntaxPlugins = process.env.BABEL_8_BREAKING\n  ? ([] as const)\n  : ([\"syntax-import-assertions\", \"syntax-import-attributes\"] as const);\n\n// use intermediary object to enforce alphabetical key order\nconst pluginSyntaxObject = process.env.BABEL_8_BREAKING\n  ? {}\n  : ({\n      \"transform-async-generator-functions\": \"syntax-async-generators\",\n      \"transform-class-properties\": \"syntax-class-properties\",\n      \"transform-class-static-block\": \"syntax-class-static-block\",\n      \"transform-export-namespace-from\": \"syntax-export-namespace-from\",\n      \"transform-json-strings\": \"syntax-json-strings\",\n      \"transform-nullish-coalescing-operator\":\n        \"syntax-nullish-coalescing-operator\",\n      \"transform-numeric-separator\": \"syntax-numeric-separator\",\n      \"transform-object-rest-spread\": \"syntax-object-rest-spread\",\n      \"transform-optional-catch-binding\": \"syntax-optional-catch-binding\",\n      \"transform-optional-chaining\": \"syntax-optional-chaining\",\n      // note: we don't have syntax-private-methods\n      \"transform-private-methods\": \"syntax-class-properties\",\n      \"transform-private-property-in-object\":\n        \"syntax-private-property-in-object\",\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      \"transform-unicode-property-regex\": null as null,\n    } as const);\n\ntype PluginSyntaxObjectKeys = keyof typeof pluginSyntaxObject;\n\nconst pluginSyntaxEntries = Object.keys(pluginSyntaxObject).map<\n  [PluginSyntaxObjectKeys, string | null]\n>(function (key: PluginSyntaxObjectKeys) {\n  return [key, pluginSyntaxObject[key]];\n});\n\nconst pluginSyntaxMap = new Map(pluginSyntaxEntries);\n\nexport { proposalPlugins, proposalSyntaxPlugins, pluginSyntaxMap };\n"], "mappings": ";;;;;;AAKA,MAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,IAAIE,GAAG,CAAS,EAAE,CAAC;AAI3C,MAAMC,qBAAqB,GAAAF,OAAA,CAAAE,qBAAA,GAEtB,CAAC,0BAA0B,EAAE,0BAA0B,CAAW;AAGvE,MAAMC,kBAAkB,GAEnB;EACC,qCAAqC,EAAE,yBAAyB;EAChE,4BAA4B,EAAE,yBAAyB;EACvD,8BAA8B,EAAE,2BAA2B;EAC3D,iCAAiC,EAAE,8BAA8B;EACjE,wBAAwB,EAAE,qBAAqB;EAC/C,uCAAuC,EACrC,oCAAoC;EACtC,6BAA6B,EAAE,0BAA0B;EACzD,8BAA8B,EAAE,2BAA2B;EAC3D,kCAAkC,EAAE,+BAA+B;EACnE,6BAA6B,EAAE,0BAA0B;EAEzD,2BAA2B,EAAE,yBAAyB;EACtD,sCAAsC,EACpC,mCAAmC;EAErC,kCAAkC,EAAE;AACtC,CAAW;AAIf,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACH,kBAAkB,CAAC,CAACI,GAAG,CAE7D,UAAUC,GAA2B,EAAE;EACvC,OAAO,CAACA,GAAG,EAAEL,kBAAkB,CAACK,GAAG,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,MAAMC,eAAe,GAAAT,OAAA,CAAAS,eAAA,GAAG,IAAIC,GAAG,CAACN,mBAAmB,CAAC", "ignoreList": []}