{"name": "jest-message-util", "version": "30.0.0-beta.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-message-util"}, "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "30.0.0-beta.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.8", "pretty-format": "30.0.0-beta.3", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "devDependencies": {"@types/babel__code-frame": "^7.0.0", "@types/graceful-fs": "^4.1.3", "@types/micromatch": "^4.0.7", "tempy": "^1.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017"}