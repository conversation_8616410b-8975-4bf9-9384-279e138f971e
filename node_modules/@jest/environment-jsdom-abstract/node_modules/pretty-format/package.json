{"name": "pretty-format", "version": "30.0.0-beta.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/pretty-format"}, "license": "MIT", "description": "Stringify any JavaScript value.", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@jest/schemas": "30.0.0-beta.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-is": "^18.0.0", "@types/react-test-renderer": "^18.0.1", "immutable": "^5.0.0", "jest-util": "30.0.0-beta.3", "react": "18.2.0", "react-dom": "18.2.0", "react-test-renderer": "18.2.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017"}