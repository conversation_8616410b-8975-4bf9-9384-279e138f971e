{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import {supportedPlatforms} from '@react-native-community/cli-config-apple';\n\ntype ObjectValues<T> = T[keyof T];\n\nexport type ApplePlatform = ObjectValues<typeof supportedPlatforms>;\nexport interface Device {\n  name: string;\n  udid: string;\n  state?: string;\n  availability?: string;\n  isAvailable?: boolean;\n  version?: string;\n  sdk?: string;\n  availabilityError?: string;\n  type?: DeviceType;\n  lastBootedAt?: string;\n}\n\nexport type DeviceType = 'simulator' | 'device' | 'catalyst';\n\nexport interface IosInfo {\n  name: string;\n  schemes?: string[];\n  configurations?: string[];\n  targets?: string[];\n}\n\nexport interface BuilderCommand {\n  /**\n   * Lowercase name of the platform.\n   * Example: 'ios', 'visionos'\n   */\n  platformName: ApplePlatform;\n}\n"], "mappings": ""}