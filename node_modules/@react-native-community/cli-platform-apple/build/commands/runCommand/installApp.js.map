{"version": 3, "names": ["handleLaunchResult", "success", "errorMessage", "errorDetails", "logger", "error", "installApp", "buildOutput", "xcodeProject", "mode", "scheme", "target", "udid", "binaryPath", "platform", "isSimulator", "appPath", "buildSettings", "getBuildSettings", "CLIError", "getBuildPath", "targetBuildDir", "TARGET_BUILD_DIR", "infoPlistPath", "INFOPLIST_PATH", "info", "chalk", "bold", "installParameters", "child_process", "spawnSync", "stdio", "bundleID", "execFileSync", "path", "join", "encoding", "trim", "launchParameters", "result", "status", "stderr", "toString"], "sources": ["../../../src/commands/runCommand/installApp.ts"], "sourcesContent": ["import child_process from 'child_process';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport {getBuildPath} from './getBuildPath';\nimport {getBuildSettings} from './getBuildSettings';\nimport path from 'path';\nimport {ApplePlatform} from '../../types';\n\nfunction handleLaunchResult(\n  success: boolean,\n  errorMessage: string,\n  errorDetails = '',\n) {\n  if (success) {\n    logger.success('Successfully launched the app');\n  } else {\n    logger.error(errorMessage, errorDetails);\n  }\n}\n\ntype Options = {\n  buildOutput: string;\n  xcodeProject: IOSProjectInfo;\n  mode: string;\n  scheme: string;\n  target?: string;\n  udid: string;\n  binaryPath?: string;\n  platform?: ApplePlatform;\n  isSimulator?: boolean;\n};\n\nexport default async function installApp({\n  buildOutput,\n  xcodeProject,\n  mode,\n  scheme,\n  target,\n  udid,\n  binaryPath,\n  platform,\n  isSimulator = true,\n}: Options) {\n  let appPath = binaryPath;\n\n  const buildSettings = await getBuildSettings(\n    xcodeProject,\n    mode,\n    buildOutput,\n    scheme,\n    target,\n  );\n\n  if (!buildSettings) {\n    throw new CLIError('Failed to get build settings for your project');\n  }\n\n  if (!appPath) {\n    appPath = await getBuildPath(buildSettings, platform);\n  }\n\n  const targetBuildDir = buildSettings.TARGET_BUILD_DIR;\n  const infoPlistPath = buildSettings.INFOPLIST_PATH;\n\n  if (!infoPlistPath) {\n    throw new CLIError('Failed to find Info.plist');\n  }\n\n  if (!targetBuildDir) {\n    throw new CLIError('Failed to get target build directory.');\n  }\n\n  logger.info(`Installing \"${chalk.bold(appPath)}`);\n\n  if (udid && appPath) {\n    const installParameters = isSimulator\n      ? ['simctl', 'install', udid, appPath]\n      : ['devicectl', 'device', 'install', 'app', '--device', udid, appPath];\n\n    child_process.spawnSync('xcrun', installParameters, {\n      stdio: 'inherit',\n    });\n  }\n\n  const bundleID = child_process\n    .execFileSync(\n      '/usr/libexec/PlistBuddy',\n      [\n        '-c',\n        'Print:CFBundleIdentifier',\n        path.join(targetBuildDir, infoPlistPath),\n      ],\n      {encoding: 'utf8'},\n    )\n    .trim();\n\n  logger.info(`Launching \"${chalk.bold(bundleID)}\"`);\n\n  const launchParameters = isSimulator\n    ? ['simctl', 'launch', udid, bundleID]\n    : ['devicectl', 'device', 'process', 'launch', '--device', udid, bundleID];\n\n  const result = child_process.spawnSync('xcrun', launchParameters);\n\n  handleLaunchResult(\n    result.status === 0,\n    'Failed to launch the app on simulator',\n    result.stderr.toString(),\n  );\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAGxB,SAASA,kBAAkB,CACzBC,OAAgB,EAChBC,YAAoB,EACpBC,YAAY,GAAG,EAAE,EACjB;EACA,IAAIF,OAAO,EAAE;IACXG,kBAAM,CAACH,OAAO,CAAC,+BAA+B,CAAC;EACjD,CAAC,MAAM;IACLG,kBAAM,CAACC,KAAK,CAACH,YAAY,EAAEC,YAAY,CAAC;EAC1C;AACF;AAce,eAAeG,UAAU,CAAC;EACvCC,WAAW;EACXC,YAAY;EACZC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,IAAI;EACJC,UAAU;EACVC,QAAQ;EACRC,WAAW,GAAG;AACP,CAAC,EAAE;EACV,IAAIC,OAAO,GAAGH,UAAU;EAExB,MAAMI,aAAa,GAAG,MAAM,IAAAC,kCAAgB,EAC1CV,YAAY,EACZC,IAAI,EACJF,WAAW,EACXG,MAAM,EACNC,MAAM,CACP;EAED,IAAI,CAACM,aAAa,EAAE;IAClB,MAAM,KAAIE,oBAAQ,EAAC,+CAA+C,CAAC;EACrE;EAEA,IAAI,CAACH,OAAO,EAAE;IACZA,OAAO,GAAG,MAAM,IAAAI,0BAAY,EAACH,aAAa,EAAEH,QAAQ,CAAC;EACvD;EAEA,MAAMO,cAAc,GAAGJ,aAAa,CAACK,gBAAgB;EACrD,MAAMC,aAAa,GAAGN,aAAa,CAACO,cAAc;EAElD,IAAI,CAACD,aAAa,EAAE;IAClB,MAAM,KAAIJ,oBAAQ,EAAC,2BAA2B,CAAC;EACjD;EAEA,IAAI,CAACE,cAAc,EAAE;IACnB,MAAM,KAAIF,oBAAQ,EAAC,uCAAuC,CAAC;EAC7D;EAEAf,kBAAM,CAACqB,IAAI,CAAE,eAAcC,gBAAK,CAACC,IAAI,CAACX,OAAO,CAAE,EAAC,CAAC;EAEjD,IAAIJ,IAAI,IAAII,OAAO,EAAE;IACnB,MAAMY,iBAAiB,GAAGb,WAAW,GACjC,CAAC,QAAQ,EAAE,SAAS,EAAEH,IAAI,EAAEI,OAAO,CAAC,GACpC,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAEJ,IAAI,EAAEI,OAAO,CAAC;IAExEa,wBAAa,CAACC,SAAS,CAAC,OAAO,EAAEF,iBAAiB,EAAE;MAClDG,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EAEA,MAAMC,QAAQ,GAAGH,wBAAa,CAC3BI,YAAY,CACX,yBAAyB,EACzB,CACE,IAAI,EACJ,0BAA0B,EAC1BC,eAAI,CAACC,IAAI,CAACd,cAAc,EAAEE,aAAa,CAAC,CACzC,EACD;IAACa,QAAQ,EAAE;EAAM,CAAC,CACnB,CACAC,IAAI,EAAE;EAETjC,kBAAM,CAACqB,IAAI,CAAE,cAAaC,gBAAK,CAACC,IAAI,CAACK,QAAQ,CAAE,GAAE,CAAC;EAElD,MAAMM,gBAAgB,GAAGvB,WAAW,GAChC,CAAC,QAAQ,EAAE,QAAQ,EAAEH,IAAI,EAAEoB,QAAQ,CAAC,GACpC,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAEpB,IAAI,EAAEoB,QAAQ,CAAC;EAE5E,MAAMO,MAAM,GAAGV,wBAAa,CAACC,SAAS,CAAC,OAAO,EAAEQ,gBAAgB,CAAC;EAEjEtC,kBAAkB,CAChBuC,MAAM,CAACC,MAAM,KAAK,CAAC,EACnB,uCAAuC,EACvCD,MAAM,CAACE,MAAM,CAACC,QAAQ,EAAE,CACzB;AACH"}