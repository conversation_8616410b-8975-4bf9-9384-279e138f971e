{"version": 3, "names": ["openApp", "buildOutput", "xcodeProject", "mode", "scheme", "target", "binaryPath", "appPath", "buildSettings", "getBuildSettings", "CLIError", "getBuildPath", "logger", "info", "chalk", "bold", "execa", "success", "e", "error"], "sources": ["../../../src/commands/runCommand/openApp.ts"], "sourcesContent": ["import {CLIError, logger} from '@react-native-community/cli-tools';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport {getBuildPath} from './getBuildPath';\nimport {getBuildSettings} from './getBuildSettings';\nimport execa from 'execa';\n\ntype Options = {\n  buildOutput: string;\n  xcodeProject: IOSProjectInfo;\n  mode: string;\n  scheme: string;\n  target?: string;\n  binaryPath?: string;\n};\n\nexport default async function openApp({\n  buildOutput,\n  xcodeProject,\n  mode,\n  scheme,\n  target,\n  binaryPath,\n}: Options) {\n  let appPath = binaryPath;\n\n  const buildSettings = await getBuildSettings(\n    xcodeProject,\n    mode,\n    buildOutput,\n    scheme,\n    target,\n  );\n\n  if (!buildSettings) {\n    throw new CLIError('Failed to get build settings for your project');\n  }\n\n  if (!appPath) {\n    appPath = await getBuildPath(buildSettings, 'macos');\n  }\n\n  logger.info(`Opening \"${chalk.bold(appPath)}\"`);\n\n  try {\n    await execa(`open ${appPath}`);\n    logger.success('Successfully launched the app');\n  } catch (e) {\n    logger.error('Failed to launch the app', e as string);\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAWX,eAAeA,OAAO,CAAC;EACpCC,WAAW;EACXC,YAAY;EACZC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC;AACO,CAAC,EAAE;EACV,IAAIC,OAAO,GAAGD,UAAU;EAExB,MAAME,aAAa,GAAG,MAAM,IAAAC,kCAAgB,EAC1CP,YAAY,EACZC,IAAI,EACJF,WAAW,EACXG,MAAM,EACNC,MAAM,CACP;EAED,IAAI,CAACG,aAAa,EAAE;IAClB,MAAM,KAAIE,oBAAQ,EAAC,+CAA+C,CAAC;EACrE;EAEA,IAAI,CAACH,OAAO,EAAE;IACZA,OAAO,GAAG,MAAM,IAAAI,0BAAY,EAACH,aAAa,EAAE,OAAO,CAAC;EACtD;EAEAI,kBAAM,CAACC,IAAI,CAAE,YAAWC,gBAAK,CAACC,IAAI,CAACR,OAAO,CAAE,GAAE,CAAC;EAE/C,IAAI;IACF,MAAM,IAAAS,gBAAK,EAAE,QAAOT,OAAQ,EAAC,CAAC;IAC9BK,kBAAM,CAACK,OAAO,CAAC,+BAA+B,CAAC;EACjD,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVN,kBAAM,CAACO,KAAK,CAAC,0BAA0B,EAAED,CAAC,CAAW;EACvD;AACF"}