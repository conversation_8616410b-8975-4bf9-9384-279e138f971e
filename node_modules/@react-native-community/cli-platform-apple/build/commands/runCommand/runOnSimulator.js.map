{"version": 3, "names": ["runOnSimulator", "xcodeProject", "platform", "mode", "scheme", "args", "simulator", "binaryPath", "target", "activeDeveloperDir", "child_process", "execFileSync", "encoding", "trim", "udid", "state", "bootSimulator", "buildOutput", "buildProject", "installApp", "selectedSimulator", "simulatorFullName", "formattedDeviceName", "logger", "info", "spawnSync"], "sources": ["../../../src/commands/runCommand/runOnSimulator.ts"], "sourcesContent": ["import child_process from 'child_process';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport {logger} from '@react-native-community/cli-tools';\nimport {ApplePlatform, Device} from '../../types';\nimport {buildProject} from '../buildCommand/buildProject';\nimport {formattedDeviceName} from './matchingDevice';\nimport {FlagsT} from './createRun';\nimport installApp from './installApp';\n\nexport async function runOnSimulator(\n  xcodeProject: IOSProjectInfo,\n  platform: ApplePlatform,\n  mode: string,\n  scheme: string,\n  args: FlagsT,\n  simulator: Device,\n) {\n  const {binaryPath, target} = args;\n\n  /**\n   * Booting simulator through `xcrun simctl boot` will boot it in the `headless` mode\n   * (running in the background).\n   *\n   * In order for user to see the app and the simulator itself, we have to make sure\n   * that the Simulator.app is running.\n   *\n   * We also pass it `-CurrentDeviceUDID` so that when we launch it for the first time,\n   * it will not boot the \"default\" device, but the one we set. If the app is already running,\n   * this flag has no effect.\n   */\n  const activeDeveloperDir = child_process\n    .execFileSync('xcode-select', ['-p'], {encoding: 'utf8'})\n    .trim();\n\n  child_process.execFileSync('open', [\n    `${activeDeveloperDir}/Applications/Simulator.app`,\n    '--args',\n    '-CurrentDeviceUDID',\n    simulator.udid,\n  ]);\n\n  if (simulator.state !== 'Booted') {\n    bootSimulator(simulator);\n  }\n\n  let buildOutput;\n  if (!binaryPath) {\n    buildOutput = await buildProject(\n      xcodeProject,\n      platform,\n      simulator.udid,\n      mode,\n      scheme,\n      args,\n    );\n  }\n\n  installApp({\n    buildOutput: buildOutput ?? '',\n    xcodeProject,\n    mode,\n    scheme,\n    target,\n    udid: simulator.udid,\n    binaryPath,\n  });\n}\n\nfunction bootSimulator(selectedSimulator: Device) {\n  const simulatorFullName = formattedDeviceName(selectedSimulator);\n  logger.info(`Launching ${simulatorFullName}`);\n\n  child_process.spawnSync('xcrun', ['simctl', 'boot', selectedSimulator.udid]);\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAEA;AAAsC;AAE/B,eAAeA,cAAc,CAClCC,YAA4B,EAC5BC,QAAuB,EACvBC,IAAY,EACZC,MAAc,EACdC,IAAY,EACZC,SAAiB,EACjB;EACA,MAAM;IAACC,UAAU;IAAEC;EAAM,CAAC,GAAGH,IAAI;;EAEjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMI,kBAAkB,GAAGC,wBAAa,CACrCC,YAAY,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,EAAE;IAACC,QAAQ,EAAE;EAAM,CAAC,CAAC,CACxDC,IAAI,EAAE;EAETH,wBAAa,CAACC,YAAY,CAAC,MAAM,EAAE,CAChC,GAAEF,kBAAmB,6BAA4B,EAClD,QAAQ,EACR,oBAAoB,EACpBH,SAAS,CAACQ,IAAI,CACf,CAAC;EAEF,IAAIR,SAAS,CAACS,KAAK,KAAK,QAAQ,EAAE;IAChCC,aAAa,CAACV,SAAS,CAAC;EAC1B;EAEA,IAAIW,WAAW;EACf,IAAI,CAACV,UAAU,EAAE;IACfU,WAAW,GAAG,MAAM,IAAAC,0BAAY,EAC9BjB,YAAY,EACZC,QAAQ,EACRI,SAAS,CAACQ,IAAI,EACdX,IAAI,EACJC,MAAM,EACNC,IAAI,CACL;EACH;EAEA,IAAAc,mBAAU,EAAC;IACTF,WAAW,EAAEA,WAAW,IAAI,EAAE;IAC9BhB,YAAY;IACZE,IAAI;IACJC,MAAM;IACNI,MAAM;IACNM,IAAI,EAAER,SAAS,CAACQ,IAAI;IACpBP;EACF,CAAC,CAAC;AACJ;AAEA,SAASS,aAAa,CAACI,iBAAyB,EAAE;EAChD,MAAMC,iBAAiB,GAAG,IAAAC,mCAAmB,EAACF,iBAAiB,CAAC;EAChEG,kBAAM,CAACC,IAAI,CAAE,aAAYH,iBAAkB,EAAC,CAAC;EAE7CX,wBAAa,CAACe,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAEL,iBAAiB,CAACN,IAAI,CAAC,CAAC;AAC9E"}