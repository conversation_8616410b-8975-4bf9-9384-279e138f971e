{"version": 3, "names": ["getPackageJson", "root", "require", "path", "join", "CLIError", "createRun", "platformName", "_", "ctx", "args", "link", "setPlatform", "platformConfig", "project", "sdkNames", "readableName", "platformReadableName", "getPlatformInfo", "undefined", "supportedPlatforms", "packager", "port", "installedPods", "automaticPodsInstallation", "forcePods", "isAppRunningNewArchitecture", "sourceDir", "getArchitecture", "resolvePods", "dependencies", "forceInstall", "newArchEnabled", "newPort", "startPackager", "findDevServerPort", "startServerInNewWindow", "reactNativePath", "terminal", "reactNativeVersion", "setVersion", "xcodeProject", "getXcodeProjectAndDir", "process", "chdir", "binaryPath", "isAbsolute", "fs", "existsSync", "mode", "scheme", "getConfiguration", "buildOutput", "buildProject", "openApp", "target", "devices", "listDevices", "length", "logger", "error", "packageJson", "preferredDevice", "cacheManager", "get", "name", "preferredDeviceIndex", "findIndex", "udid", "device", "splice", "unshift", "fallbackSimulator", "getFallbackSimulator", "interactive", "warn", "selected<PERSON><PERSON><PERSON>", "promptForDeviceSelection", "set", "type", "runOnSimulator", "runOnDevice", "simulator", "bootedSimulators", "filter", "state", "bootedDevices", "booted", "info", "map", "find", "d", "chalk", "bold", "printFoundDevices", "matchingDevice", "deviceByUdid"], "sources": ["../../../src/commands/runCommand/createRun.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport fs from 'fs';\nimport chalk from 'chalk';\nimport {Config, IOSProjectConfig} from '@react-native-community/cli-types';\nimport {\n  logger,\n  CLIError,\n  link,\n  startServerInNewWindow,\n  findDevServerPort,\n  cacheManager,\n} from '@react-native-community/cli-tools';\nimport getArchitecture from '../../tools/getArchitecture';\nimport listDevices from '../../tools/listDevices';\nimport {promptForDeviceSelection} from '../../tools/prompts';\nimport {BuildFlags} from '../buildCommand/buildOptions';\nimport {buildProject} from '../buildCommand/buildProject';\nimport {getConfiguration} from '../buildCommand/getConfiguration';\nimport {getXcodeProjectAndDir} from '../buildCommand/getXcodeProjectAndDir';\nimport {getFallbackSimulator} from './getFallbackSimulator';\nimport {getPlatformInfo} from './getPlatformInfo';\nimport {printFoundDevices, matchingDevice} from './matchingDevice';\nimport {runOnDevice} from './runOnDevice';\nimport {runOnSimulator} from './runOnSimulator';\nimport {BuilderCommand} from '../../types';\nimport {\n  supportedPlatforms,\n  resolvePods,\n} from '@react-native-community/cli-config-apple';\nimport openApp from './openApp';\n\nexport interface FlagsT extends BuildFlags {\n  simulator?: string;\n  device?: string | true;\n  udid?: string;\n  binaryPath?: string;\n  listDevices?: boolean;\n  packager?: boolean;\n  port: number;\n  terminal?: string;\n}\n\nfunction getPackageJson(root: string) {\n  try {\n    return require(path.join(root, 'package.json'));\n  } catch {\n    throw new CLIError(\n      'No package.json found. Please make sure the file exists in the current folder.',\n    );\n  }\n}\n\nconst createRun =\n  ({platformName}: BuilderCommand) =>\n  async (_: Array<string>, ctx: Config, args: FlagsT) => {\n    // React Native docs assume platform is always ios/android\n    link.setPlatform('ios');\n    const platformConfig = ctx.project[platformName] as IOSProjectConfig;\n    const {sdkNames, readableName: platformReadableName} =\n      getPlatformInfo(platformName);\n\n    if (\n      platformConfig === undefined ||\n      supportedPlatforms[platformName] === undefined\n    ) {\n      throw new CLIError(\n        `Unable to find ${platformReadableName} platform config`,\n      );\n    }\n\n    let {packager, port} = args;\n    let installedPods = false;\n    // check if pods need to be installed\n    if (platformConfig.automaticPodsInstallation || args.forcePods) {\n      const isAppRunningNewArchitecture = platformConfig.sourceDir\n        ? await getArchitecture(platformConfig.sourceDir)\n        : undefined;\n\n      await resolvePods(\n        ctx.root,\n        platformConfig.sourceDir,\n        ctx.dependencies,\n        platformName,\n        {\n          forceInstall: args.forcePods,\n          newArchEnabled: isAppRunningNewArchitecture,\n        },\n      );\n\n      installedPods = true;\n    }\n\n    if (packager) {\n      const {port: newPort, startPackager} = await findDevServerPort(\n        port,\n        ctx.root,\n      );\n\n      if (startPackager) {\n        await startServerInNewWindow(\n          newPort,\n          ctx.root,\n          ctx.reactNativePath,\n          args.terminal,\n        );\n      }\n    }\n\n    if (ctx.reactNativeVersion !== 'unknown') {\n      link.setVersion(ctx.reactNativeVersion);\n    }\n\n    let {xcodeProject, sourceDir} = getXcodeProjectAndDir(\n      platformConfig,\n      platformName,\n      installedPods,\n    );\n\n    process.chdir(sourceDir);\n\n    if (args.binaryPath) {\n      args.binaryPath = path.isAbsolute(args.binaryPath)\n        ? args.binaryPath\n        : path.join(ctx.root, args.binaryPath);\n\n      if (!fs.existsSync(args.binaryPath)) {\n        throw new CLIError(\n          'binary-path was specified, but the file was not found.',\n        );\n      }\n    }\n\n    const {mode, scheme} = await getConfiguration(\n      xcodeProject,\n      sourceDir,\n      args,\n      platformName,\n    );\n\n    if (platformName === 'macos') {\n      const buildOutput = await buildProject(\n        xcodeProject,\n        platformName,\n        undefined,\n        mode,\n        scheme,\n        args,\n      );\n\n      openApp({\n        buildOutput,\n        xcodeProject,\n        mode,\n        scheme,\n        target: args.target,\n        binaryPath: args.binaryPath,\n      });\n\n      return;\n    }\n\n    let devices = await listDevices(sdkNames);\n\n    if (devices.length === 0) {\n      return logger.error(\n        `${platformReadableName} devices or simulators not detected. Install simulators via Xcode or connect a physical ${platformReadableName} device`,\n      );\n    }\n\n    const packageJson = getPackageJson(ctx.root);\n\n    const preferredDevice = cacheManager.get(\n      packageJson.name,\n      'lastUsedIOSDeviceId',\n    );\n\n    if (preferredDevice) {\n      const preferredDeviceIndex = devices.findIndex(\n        ({udid}) => udid === preferredDevice,\n      );\n\n      if (preferredDeviceIndex > -1) {\n        const [device] = devices.splice(preferredDeviceIndex, 1);\n        devices.unshift(device);\n      }\n    }\n\n    const fallbackSimulator =\n      platformName === 'ios' || platformName === 'tvos'\n        ? getFallbackSimulator(args)\n        : devices[0];\n\n    if (args.listDevices || args.interactive) {\n      if (args.device || args.udid) {\n        logger.warn(\n          `Both ${\n            args.device ? 'device' : 'udid'\n          } and \"list-devices\" parameters were passed to \"run\" command. We will list available devices and let you choose from one.`,\n        );\n      }\n\n      const selectedDevice = await promptForDeviceSelection(devices);\n\n      if (!selectedDevice) {\n        throw new CLIError(\n          `Failed to select device, please try to run app without ${\n            args.listDevices ? 'list-devices' : 'interactive'\n          } command.`,\n        );\n      } else {\n        if (selectedDevice.udid !== preferredDevice) {\n          cacheManager.set(\n            packageJson.name,\n            'lastUsedIOSDeviceId',\n            selectedDevice.udid,\n          );\n        }\n      }\n\n      if (selectedDevice.type === 'simulator') {\n        return runOnSimulator(\n          xcodeProject,\n          platformName,\n          mode,\n          scheme,\n          args,\n          selectedDevice,\n        );\n      } else {\n        return runOnDevice(\n          selectedDevice,\n          platformName,\n          mode,\n          scheme,\n          xcodeProject,\n          args,\n        );\n      }\n    }\n\n    if (!args.device && !args.udid && !args.simulator) {\n      const bootedSimulators = devices.filter(\n        ({state, type}) => state === 'Booted' && type === 'simulator',\n      );\n      const bootedDevices = devices.filter(({type}) => type === 'device'); // Physical devices here are always booted\n      const booted = [...bootedSimulators, ...bootedDevices];\n\n      if (booted.length === 0) {\n        logger.info(\n          'No booted devices or simulators found. Launching first available simulator...',\n        );\n        return runOnSimulator(\n          xcodeProject,\n          platformName,\n          mode,\n          scheme,\n          args,\n          fallbackSimulator,\n        );\n      }\n\n      logger.info(`Found booted ${booted.map(({name}) => name).join(', ')}`);\n\n      for (const simulator of bootedSimulators) {\n        await runOnSimulator(\n          xcodeProject,\n          platformName,\n          mode,\n          scheme,\n          args,\n          simulator || fallbackSimulator,\n        );\n      }\n\n      for (const device of bootedDevices) {\n        await runOnDevice(\n          device,\n          platformName,\n          mode,\n          scheme,\n          xcodeProject,\n          args,\n        );\n      }\n\n      return;\n    }\n\n    if (args.device && args.udid) {\n      return logger.error(\n        'The `device` and `udid` options are mutually exclusive.',\n      );\n    }\n\n    if (args.udid) {\n      const device = devices.find((d) => d.udid === args.udid);\n      if (!device) {\n        return logger.error(\n          `Could not find a device with udid: \"${chalk.bold(\n            args.udid,\n          )}\". ${printFoundDevices(devices)}`,\n        );\n      }\n      if (device.type === 'simulator') {\n        return runOnSimulator(\n          xcodeProject,\n          platformName,\n          mode,\n          scheme,\n          args,\n          fallbackSimulator,\n        );\n      } else {\n        return runOnDevice(\n          device,\n          platformName,\n          mode,\n          scheme,\n          xcodeProject,\n          args,\n        );\n      }\n    } else if (args.device) {\n      let device = matchingDevice(devices, args.device);\n\n      if (!device) {\n        const deviceByUdid = devices.find((d) => d.udid === args.device);\n        if (!deviceByUdid) {\n          return logger.error(\n            `Could not find a physical device with name or unique device identifier: \"${chalk.bold(\n              args.device,\n            )}\". ${printFoundDevices(devices, 'device')}`,\n          );\n        }\n\n        device = deviceByUdid;\n\n        if (deviceByUdid.type === 'simulator') {\n          return logger.error(\n            `The device with udid: \"${chalk.bold(\n              args.device,\n            )}\" is a simulator. If you want to run on a simulator, use the \"--simulator\" flag instead.`,\n          );\n        }\n      }\n\n      if (device && device.type === 'simulator') {\n        return logger.error(\n          \"`--device` flag is intended for physical devices. If you're trying to run on a simulator, use `--simulator` instead.\",\n        );\n      }\n\n      if (device && device.type === 'device') {\n        return runOnDevice(\n          device,\n          platformName,\n          mode,\n          scheme,\n          xcodeProject,\n          args,\n        );\n      }\n    } else {\n      runOnSimulator(\n        xcodeProject,\n        platformName,\n        mode,\n        scheme,\n        args,\n        fallbackSimulator,\n      );\n    }\n  };\n\nexport default createRun;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;AAAgC;AArChC;AACA;AACA;AACA;AACA;AACA;AACA;;AA4CA,SAASA,cAAc,CAACC,IAAY,EAAE;EACpC,IAAI;IACF,OAAOC,OAAO,CAACC,eAAI,CAACC,IAAI,CAACH,IAAI,EAAE,cAAc,CAAC,CAAC;EACjD,CAAC,CAAC,MAAM;IACN,MAAM,KAAII,oBAAQ,EAChB,gFAAgF,CACjF;EACH;AACF;AAEA,MAAMC,SAAS,GACb,CAAC;EAACC;AAA4B,CAAC,KAC/B,OAAOC,CAAgB,EAAEC,GAAW,EAAEC,IAAY,KAAK;EACrD;EACAC,gBAAI,CAACC,WAAW,CAAC,KAAK,CAAC;EACvB,MAAMC,cAAc,GAAGJ,GAAG,CAACK,OAAO,CAACP,YAAY,CAAqB;EACpE,MAAM;IAACQ,QAAQ;IAAEC,YAAY,EAAEC;EAAoB,CAAC,GAClD,IAAAC,gCAAe,EAACX,YAAY,CAAC;EAE/B,IACEM,cAAc,KAAKM,SAAS,IAC5BC,oCAAkB,CAACb,YAAY,CAAC,KAAKY,SAAS,EAC9C;IACA,MAAM,KAAId,oBAAQ,EACf,kBAAiBY,oBAAqB,kBAAiB,CACzD;EACH;EAEA,IAAI;IAACI,QAAQ;IAAEC;EAAI,CAAC,GAAGZ,IAAI;EAC3B,IAAIa,aAAa,GAAG,KAAK;EACzB;EACA,IAAIV,cAAc,CAACW,yBAAyB,IAAId,IAAI,CAACe,SAAS,EAAE;IAC9D,MAAMC,2BAA2B,GAAGb,cAAc,CAACc,SAAS,GACxD,MAAM,IAAAC,wBAAe,EAACf,cAAc,CAACc,SAAS,CAAC,GAC/CR,SAAS;IAEb,MAAM,IAAAU,6BAAW,EACfpB,GAAG,CAACR,IAAI,EACRY,cAAc,CAACc,SAAS,EACxBlB,GAAG,CAACqB,YAAY,EAChBvB,YAAY,EACZ;MACEwB,YAAY,EAAErB,IAAI,CAACe,SAAS;MAC5BO,cAAc,EAAEN;IAClB,CAAC,CACF;IAEDH,aAAa,GAAG,IAAI;EACtB;EAEA,IAAIF,QAAQ,EAAE;IACZ,MAAM;MAACC,IAAI,EAAEW,OAAO;MAAEC;IAAa,CAAC,GAAG,MAAM,IAAAC,6BAAiB,EAC5Db,IAAI,EACJb,GAAG,CAACR,IAAI,CACT;IAED,IAAIiC,aAAa,EAAE;MACjB,MAAM,IAAAE,kCAAsB,EAC1BH,OAAO,EACPxB,GAAG,CAACR,IAAI,EACRQ,GAAG,CAAC4B,eAAe,EACnB3B,IAAI,CAAC4B,QAAQ,CACd;IACH;EACF;EAEA,IAAI7B,GAAG,CAAC8B,kBAAkB,KAAK,SAAS,EAAE;IACxC5B,gBAAI,CAAC6B,UAAU,CAAC/B,GAAG,CAAC8B,kBAAkB,CAAC;EACzC;EAEA,IAAI;IAACE,YAAY;IAAEd;EAAS,CAAC,GAAG,IAAAe,4CAAqB,EACnD7B,cAAc,EACdN,YAAY,EACZgB,aAAa,CACd;EAEDoB,OAAO,CAACC,KAAK,CAACjB,SAAS,CAAC;EAExB,IAAIjB,IAAI,CAACmC,UAAU,EAAE;IACnBnC,IAAI,CAACmC,UAAU,GAAG1C,eAAI,CAAC2C,UAAU,CAACpC,IAAI,CAACmC,UAAU,CAAC,GAC9CnC,IAAI,CAACmC,UAAU,GACf1C,eAAI,CAACC,IAAI,CAACK,GAAG,CAACR,IAAI,EAAES,IAAI,CAACmC,UAAU,CAAC;IAExC,IAAI,CAACE,aAAE,CAACC,UAAU,CAACtC,IAAI,CAACmC,UAAU,CAAC,EAAE;MACnC,MAAM,KAAIxC,oBAAQ,EAChB,wDAAwD,CACzD;IACH;EACF;EAEA,MAAM;IAAC4C,IAAI;IAAEC;EAAM,CAAC,GAAG,MAAM,IAAAC,kCAAgB,EAC3CV,YAAY,EACZd,SAAS,EACTjB,IAAI,EACJH,YAAY,CACb;EAED,IAAIA,YAAY,KAAK,OAAO,EAAE;IAC5B,MAAM6C,WAAW,GAAG,MAAM,IAAAC,0BAAY,EACpCZ,YAAY,EACZlC,YAAY,EACZY,SAAS,EACT8B,IAAI,EACJC,MAAM,EACNxC,IAAI,CACL;IAED,IAAA4C,gBAAO,EAAC;MACNF,WAAW;MACXX,YAAY;MACZQ,IAAI;MACJC,MAAM;MACNK,MAAM,EAAE7C,IAAI,CAAC6C,MAAM;MACnBV,UAAU,EAAEnC,IAAI,CAACmC;IACnB,CAAC,CAAC;IAEF;EACF;EAEA,IAAIW,OAAO,GAAG,MAAM,IAAAC,oBAAW,EAAC1C,QAAQ,CAAC;EAEzC,IAAIyC,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,OAAOC,kBAAM,CAACC,KAAK,CAChB,GAAE3C,oBAAqB,2FAA0FA,oBAAqB,SAAQ,CAChJ;EACH;EAEA,MAAM4C,WAAW,GAAG7D,cAAc,CAACS,GAAG,CAACR,IAAI,CAAC;EAE5C,MAAM6D,eAAe,GAAGC,wBAAY,CAACC,GAAG,CACtCH,WAAW,CAACI,IAAI,EAChB,qBAAqB,CACtB;EAED,IAAIH,eAAe,EAAE;IACnB,MAAMI,oBAAoB,GAAGV,OAAO,CAACW,SAAS,CAC5C,CAAC;MAACC;IAAI,CAAC,KAAKA,IAAI,KAAKN,eAAe,CACrC;IAED,IAAII,oBAAoB,GAAG,CAAC,CAAC,EAAE;MAC7B,MAAM,CAACG,MAAM,CAAC,GAAGb,OAAO,CAACc,MAAM,CAACJ,oBAAoB,EAAE,CAAC,CAAC;MACxDV,OAAO,CAACe,OAAO,CAACF,MAAM,CAAC;IACzB;EACF;EAEA,MAAMG,iBAAiB,GACrBjE,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,MAAM,GAC7C,IAAAkE,0CAAoB,EAAC/D,IAAI,CAAC,GAC1B8C,OAAO,CAAC,CAAC,CAAC;EAEhB,IAAI9C,IAAI,CAAC+C,WAAW,IAAI/C,IAAI,CAACgE,WAAW,EAAE;IACxC,IAAIhE,IAAI,CAAC2D,MAAM,IAAI3D,IAAI,CAAC0D,IAAI,EAAE;MAC5BT,kBAAM,CAACgB,IAAI,CACR,QACCjE,IAAI,CAAC2D,MAAM,GAAG,QAAQ,GAAG,MAC1B,0HAAyH,CAC3H;IACH;IAEA,MAAMO,cAAc,GAAG,MAAM,IAAAC,iCAAwB,EAACrB,OAAO,CAAC;IAE9D,IAAI,CAACoB,cAAc,EAAE;MACnB,MAAM,KAAIvE,oBAAQ,EACf,0DACCK,IAAI,CAAC+C,WAAW,GAAG,cAAc,GAAG,aACrC,WAAU,CACZ;IACH,CAAC,MAAM;MACL,IAAImB,cAAc,CAACR,IAAI,KAAKN,eAAe,EAAE;QAC3CC,wBAAY,CAACe,GAAG,CACdjB,WAAW,CAACI,IAAI,EAChB,qBAAqB,EACrBW,cAAc,CAACR,IAAI,CACpB;MACH;IACF;IAEA,IAAIQ,cAAc,CAACG,IAAI,KAAK,WAAW,EAAE;MACvC,OAAO,IAAAC,8BAAc,EACnBvC,YAAY,EACZlC,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNxC,IAAI,EACJkE,cAAc,CACf;IACH,CAAC,MAAM;MACL,OAAO,IAAAK,wBAAW,EAChBL,cAAc,EACdrE,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNT,YAAY,EACZ/B,IAAI,CACL;IACH;EACF;EAEA,IAAI,CAACA,IAAI,CAAC2D,MAAM,IAAI,CAAC3D,IAAI,CAAC0D,IAAI,IAAI,CAAC1D,IAAI,CAACwE,SAAS,EAAE;IACjD,MAAMC,gBAAgB,GAAG3B,OAAO,CAAC4B,MAAM,CACrC,CAAC;MAACC,KAAK;MAAEN;IAAI,CAAC,KAAKM,KAAK,KAAK,QAAQ,IAAIN,IAAI,KAAK,WAAW,CAC9D;IACD,MAAMO,aAAa,GAAG9B,OAAO,CAAC4B,MAAM,CAAC,CAAC;MAACL;IAAI,CAAC,KAAKA,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;IACrE,MAAMQ,MAAM,GAAG,CAAC,GAAGJ,gBAAgB,EAAE,GAAGG,aAAa,CAAC;IAEtD,IAAIC,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MACvBC,kBAAM,CAAC6B,IAAI,CACT,+EAA+E,CAChF;MACD,OAAO,IAAAR,8BAAc,EACnBvC,YAAY,EACZlC,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNxC,IAAI,EACJ8D,iBAAiB,CAClB;IACH;IAEAb,kBAAM,CAAC6B,IAAI,CAAE,gBAAeD,MAAM,CAACE,GAAG,CAAC,CAAC;MAACxB;IAAI,CAAC,KAAKA,IAAI,CAAC,CAAC7D,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IAEtE,KAAK,MAAM8E,SAAS,IAAIC,gBAAgB,EAAE;MACxC,MAAM,IAAAH,8BAAc,EAClBvC,YAAY,EACZlC,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNxC,IAAI,EACJwE,SAAS,IAAIV,iBAAiB,CAC/B;IACH;IAEA,KAAK,MAAMH,MAAM,IAAIiB,aAAa,EAAE;MAClC,MAAM,IAAAL,wBAAW,EACfZ,MAAM,EACN9D,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNT,YAAY,EACZ/B,IAAI,CACL;IACH;IAEA;EACF;EAEA,IAAIA,IAAI,CAAC2D,MAAM,IAAI3D,IAAI,CAAC0D,IAAI,EAAE;IAC5B,OAAOT,kBAAM,CAACC,KAAK,CACjB,yDAAyD,CAC1D;EACH;EAEA,IAAIlD,IAAI,CAAC0D,IAAI,EAAE;IACb,MAAMC,MAAM,GAAGb,OAAO,CAACkC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACvB,IAAI,KAAK1D,IAAI,CAAC0D,IAAI,CAAC;IACxD,IAAI,CAACC,MAAM,EAAE;MACX,OAAOV,kBAAM,CAACC,KAAK,CAChB,uCAAsCgC,gBAAK,CAACC,IAAI,CAC/CnF,IAAI,CAAC0D,IAAI,CACT,MAAK,IAAA0B,iCAAiB,EAACtC,OAAO,CAAE,EAAC,CACpC;IACH;IACA,IAAIa,MAAM,CAACU,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAAC,8BAAc,EACnBvC,YAAY,EACZlC,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNxC,IAAI,EACJ8D,iBAAiB,CAClB;IACH,CAAC,MAAM;MACL,OAAO,IAAAS,wBAAW,EAChBZ,MAAM,EACN9D,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNT,YAAY,EACZ/B,IAAI,CACL;IACH;EACF,CAAC,MAAM,IAAIA,IAAI,CAAC2D,MAAM,EAAE;IACtB,IAAIA,MAAM,GAAG,IAAA0B,8BAAc,EAACvC,OAAO,EAAE9C,IAAI,CAAC2D,MAAM,CAAC;IAEjD,IAAI,CAACA,MAAM,EAAE;MACX,MAAM2B,YAAY,GAAGxC,OAAO,CAACkC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACvB,IAAI,KAAK1D,IAAI,CAAC2D,MAAM,CAAC;MAChE,IAAI,CAAC2B,YAAY,EAAE;QACjB,OAAOrC,kBAAM,CAACC,KAAK,CAChB,4EAA2EgC,gBAAK,CAACC,IAAI,CACpFnF,IAAI,CAAC2D,MAAM,CACX,MAAK,IAAAyB,iCAAiB,EAACtC,OAAO,EAAE,QAAQ,CAAE,EAAC,CAC9C;MACH;MAEAa,MAAM,GAAG2B,YAAY;MAErB,IAAIA,YAAY,CAACjB,IAAI,KAAK,WAAW,EAAE;QACrC,OAAOpB,kBAAM,CAACC,KAAK,CAChB,0BAAyBgC,gBAAK,CAACC,IAAI,CAClCnF,IAAI,CAAC2D,MAAM,CACX,0FAAyF,CAC5F;MACH;IACF;IAEA,IAAIA,MAAM,IAAIA,MAAM,CAACU,IAAI,KAAK,WAAW,EAAE;MACzC,OAAOpB,kBAAM,CAACC,KAAK,CACjB,sHAAsH,CACvH;IACH;IAEA,IAAIS,MAAM,IAAIA,MAAM,CAACU,IAAI,KAAK,QAAQ,EAAE;MACtC,OAAO,IAAAE,wBAAW,EAChBZ,MAAM,EACN9D,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNT,YAAY,EACZ/B,IAAI,CACL;IACH;EACF,CAAC,MAAM;IACL,IAAAsE,8BAAc,EACZvC,YAAY,EACZlC,YAAY,EACZ0C,IAAI,EACJC,MAAM,EACNxC,IAAI,EACJ8D,iBAAiB,CAClB;EACH;AACF,CAAC;AAAC,eAEWlE,SAAS;AAAA"}