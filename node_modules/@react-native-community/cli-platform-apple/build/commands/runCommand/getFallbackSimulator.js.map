{"version": 3, "names": ["getFallbackSimulator", "args", "fallbackSimulators", "selectedSimulator", "getDestinationSimulator", "CLIError", "simulator", "udid"], "sources": ["../../../src/commands/runCommand/getFallbackSimulator.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport {getDestinationSimulator} from '../../tools/getDestinationSimulator';\nimport {Device} from '../../types';\nimport {FlagsT} from './createRun';\n\nexport function getFallbackSimulator(args: FlagsT): Device {\n  /**\n   * If provided simulator does not exist, try simulators in following order\n   * - iPhone 14\n   * - iPhone 13\n   * - iPhone 12\n   * - iPhone 11\n   */\n\n  const fallbackSimulators = [\n    'iPhone 14',\n    'iPhone 13',\n    'iPhone 12',\n    'iPhone 11',\n  ];\n  const selectedSimulator = getDestinationSimulator(args, fallbackSimulators);\n\n  if (!selectedSimulator) {\n    throw new CLIError(\n      `No simulator available with ${\n        args.simulator ? `name \"${args.simulator}\"` : `udid \"${args.udid}\"`\n      }`,\n    );\n  }\n\n  return selectedSimulator;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAIO,SAASA,oBAAoB,CAACC,IAAY,EAAU;EACzD;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAMC,kBAAkB,GAAG,CACzB,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACZ;EACD,MAAMC,iBAAiB,GAAG,IAAAC,gDAAuB,EAACH,IAAI,EAAEC,kBAAkB,CAAC;EAE3E,IAAI,CAACC,iBAAiB,EAAE;IACtB,MAAM,KAAIE,oBAAQ,EACf,+BACCJ,IAAI,CAACK,SAAS,GAAI,SAAQL,IAAI,CAACK,SAAU,GAAE,GAAI,SAAQL,IAAI,CAACM,IAAK,GAClE,EAAC,CACH;EACH;EAEA,OAAOJ,iBAAiB;AAC1B"}