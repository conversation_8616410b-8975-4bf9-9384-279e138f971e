{"version": 3, "names": ["getPlatformInfo", "platform", "iosPlatformInfo", "readableName", "sdkNames"], "sources": ["../../../src/commands/runCommand/getPlatformInfo.ts"], "sourcesContent": ["import {ApplePlatform} from '../../types';\n\ninterface PlatformInfo {\n  readableName: string;\n  sdkNames: string[];\n}\n\n/**\n * Returns platform readable name and list of SDKs for given platform.\n * We can get list of SDKs from `xcodebuild -showsdks` command.\n *\n * Falls back to iOS if platform is not supported.\n */\nexport function getPlatformInfo(platform: ApplePlatform): PlatformInfo {\n  const iosPlatformInfo: PlatformInfo = {\n    readableName: 'iOS',\n    sdkNames: ['iphonesimulator', 'iphoneos'],\n  };\n\n  switch (platform) {\n    case 'ios':\n      return iosPlatformInfo;\n    case 'tvos':\n      return {\n        readableName: 'tvOS',\n        sdkNames: ['appletvsimulator', 'appletvos'],\n      };\n    case 'visionos':\n      return {\n        readableName: 'visionOS',\n        sdkNames: ['xrsimulator', 'xros'],\n      };\n    case 'macos':\n      return {\n        readableName: 'macOS',\n        sdkNames: ['macosx'],\n      };\n    default:\n      return iosPlatformInfo;\n  }\n}\n"], "mappings": ";;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,eAAe,CAACC,QAAuB,EAAgB;EACrE,MAAMC,eAA6B,GAAG;IACpCC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,UAAU;EAC1C,CAAC;EAED,QAAQH,QAAQ;IACd,KAAK,KAAK;MACR,OAAOC,eAAe;IACxB,KAAK,MAAM;MACT,OAAO;QACLC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,WAAW;MAC5C,CAAC;IACH,KAAK,UAAU;MACb,OAAO;QACLD,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,CAAC,aAAa,EAAE,MAAM;MAClC,CAAC;IACH,KAAK,OAAO;MACV,OAAO;QACLD,YAAY,EAAE,OAAO;QACrBC,QAAQ,EAAE,CAAC,QAAQ;MACrB,CAAC;IACH;MACE,OAAOF,eAAe;EAAC;AAE7B"}