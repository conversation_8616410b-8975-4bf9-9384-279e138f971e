{"version": 3, "names": ["getBuildSettings", "xcodeProject", "mode", "buildOutput", "scheme", "target", "buildSettings", "child_process", "execFileSync", "isWorkspace", "name", "getPlatformName", "encoding", "settings", "JSON", "parse", "targets", "map", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "includes", "logger", "info", "chalk", "bold", "targetIndex", "indexOf", "targetSettings", "wrapperExtension", "WRAPPER_EXTENSION", "platformNameMatch", "exec", "CLIError"], "sources": ["../../../src/commands/runCommand/getBuildSettings.ts"], "sourcesContent": ["import {CLIError, logger} from '@react-native-community/cli-tools';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport child_process from 'child_process';\n\nexport type BuildSettings = {\n  TARGET_BUILD_DIR: string;\n  INFOPLIST_PATH: string;\n  EXECUTABLE_FOLDER_PATH: string;\n  FULL_PRODUCT_NAME: string;\n};\n\nexport async function getBuildSettings(\n  xcodeProject: IOSProjectInfo,\n  mode: string,\n  buildOutput: string,\n  scheme: string,\n  target?: string,\n): Promise<BuildSettings | null> {\n  const buildSettings = child_process.execFileSync(\n    'xcodebuild',\n    [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      '-scheme',\n      scheme,\n      '-sdk',\n      getPlatformName(buildOutput),\n      '-configuration',\n      mode,\n      '-showBuildSettings',\n      '-json',\n    ],\n    {encoding: 'utf8'},\n  );\n\n  const settings = JSON.parse(buildSettings);\n\n  const targets = settings.map(\n    ({target: settingsTarget}: any) => settingsTarget,\n  );\n\n  let selectedTarget = targets[0];\n\n  if (target) {\n    if (!targets.includes(target)) {\n      logger.info(\n        `Target ${chalk.bold(target)} not found for scheme ${chalk.bold(\n          scheme,\n        )}, automatically selected target ${chalk.bold(selectedTarget)}`,\n      );\n    } else {\n      selectedTarget = target;\n    }\n  }\n\n  // Find app in all building settings - look for WRAPPER_EXTENSION: 'app',\n  const targetIndex = targets.indexOf(selectedTarget);\n  const targetSettings = settings[targetIndex].buildSettings;\n\n  const wrapperExtension = targetSettings.WRAPPER_EXTENSION;\n\n  if (wrapperExtension === 'app') {\n    return settings[targetIndex].buildSettings;\n  }\n\n  return null;\n}\n\nfunction getPlatformName(buildOutput: string) {\n  // Xcode can sometimes escape `=` with a backslash or put the value in quotes\n  const platformNameMatch = /export PLATFORM_NAME\\\\?=\"?(\\w+)\"?$/m.exec(\n    buildOutput,\n  );\n  if (!platformNameMatch) {\n    throw new CLIError(\n      'Couldn\\'t find \"PLATFORM_NAME\" variable in xcodebuild output. Please report this issue and run your project with Xcode instead.',\n    );\n  }\n  return platformNameMatch[1];\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0C;AASnC,eAAeA,gBAAgB,CACpCC,YAA4B,EAC5BC,IAAY,EACZC,WAAmB,EACnBC,MAAc,EACdC,MAAe,EACgB;EAC/B,MAAMC,aAAa,GAAGC,wBAAa,CAACC,YAAY,CAC9C,YAAY,EACZ,CACEP,YAAY,CAACQ,WAAW,GAAG,YAAY,GAAG,UAAU,EACpDR,YAAY,CAACS,IAAI,EACjB,SAAS,EACTN,MAAM,EACN,MAAM,EACNO,eAAe,CAACR,WAAW,CAAC,EAC5B,gBAAgB,EAChBD,IAAI,EACJ,oBAAoB,EACpB,OAAO,CACR,EACD;IAACU,QAAQ,EAAE;EAAM,CAAC,CACnB;EAED,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACT,aAAa,CAAC;EAE1C,MAAMU,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAC1B,CAAC;IAACZ,MAAM,EAAEa;EAAmB,CAAC,KAAKA,cAAc,CAClD;EAED,IAAIC,cAAc,GAAGH,OAAO,CAAC,CAAC,CAAC;EAE/B,IAAIX,MAAM,EAAE;IACV,IAAI,CAACW,OAAO,CAACI,QAAQ,CAACf,MAAM,CAAC,EAAE;MAC7BgB,kBAAM,CAACC,IAAI,CACR,UAASC,gBAAK,CAACC,IAAI,CAACnB,MAAM,CAAE,yBAAwBkB,gBAAK,CAACC,IAAI,CAC7DpB,MAAM,CACN,mCAAkCmB,gBAAK,CAACC,IAAI,CAACL,cAAc,CAAE,EAAC,CACjE;IACH,CAAC,MAAM;MACLA,cAAc,GAAGd,MAAM;IACzB;EACF;;EAEA;EACA,MAAMoB,WAAW,GAAGT,OAAO,CAACU,OAAO,CAACP,cAAc,CAAC;EACnD,MAAMQ,cAAc,GAAGd,QAAQ,CAACY,WAAW,CAAC,CAACnB,aAAa;EAE1D,MAAMsB,gBAAgB,GAAGD,cAAc,CAACE,iBAAiB;EAEzD,IAAID,gBAAgB,KAAK,KAAK,EAAE;IAC9B,OAAOf,QAAQ,CAACY,WAAW,CAAC,CAACnB,aAAa;EAC5C;EAEA,OAAO,IAAI;AACb;AAEA,SAASK,eAAe,CAACR,WAAmB,EAAE;EAC5C;EACA,MAAM2B,iBAAiB,GAAG,qCAAqC,CAACC,IAAI,CAClE5B,WAAW,CACZ;EACD,IAAI,CAAC2B,iBAAiB,EAAE;IACtB,MAAM,KAAIE,oBAAQ,EAChB,iIAAiI,CAClI;EACH;EACA,OAAOF,iBAAiB,CAAC,CAAC,CAAC;AAC7B"}