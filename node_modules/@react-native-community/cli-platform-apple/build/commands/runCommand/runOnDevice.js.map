{"version": 3, "names": ["runOnDevice", "selected<PERSON><PERSON><PERSON>", "platform", "mode", "scheme", "xcodeProject", "args", "binaryPath", "type", "CLIError", "buildOutput", "buildProject", "udid", "buildSettings", "getBuildSettings", "appPath", "getBuildPath", "appProcess", "child_process", "spawn", "detached", "stdio", "unref", "target", "logger", "info", "name", "installApp", "isSimulator", "success"], "sources": ["../../../src/commands/runCommand/runOnDevice.ts"], "sourcesContent": ["import child_process from 'child_process';\nimport {ApplePlatform, Device} from '../../types';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\nimport {buildProject} from '../buildCommand/buildProject';\nimport {getBuildPath} from './getBuildPath';\nimport {FlagsT} from './createRun';\nimport {getBuildSettings} from './getBuildSettings';\nimport installApp from './installApp';\n\nexport async function runOnDevice(\n  selectedDevice: Device,\n  platform: ApplePlatform,\n  mode: string,\n  scheme: string,\n  xcodeProject: IOSProjectInfo,\n  args: FlagsT,\n) {\n  if (args.binaryPath && selectedDevice.type === 'catalyst') {\n    throw new CLIError(\n      'binary-path was specified for catalyst device, which is not supported.',\n    );\n  }\n\n  if (selectedDevice.type === 'catalyst') {\n    const buildOutput = await buildProject(\n      xcodeProject,\n      platform,\n      selectedDevice.udid,\n      mode,\n      scheme,\n      args,\n    );\n\n    const buildSettings = await getBuildSettings(\n      xcodeProject,\n      mode,\n      buildOutput,\n      scheme,\n    );\n\n    if (!buildSettings) {\n      throw new CLIError('Failed to get build settings for your project');\n    }\n\n    const appPath = await getBuildPath(buildSettings, platform, true);\n    const appProcess = child_process.spawn(`${appPath}/${scheme}`, [], {\n      detached: true,\n      stdio: 'ignore',\n    });\n    appProcess.unref();\n  } else {\n    const {binaryPath, target} = args;\n\n    let buildOutput;\n    if (!binaryPath) {\n      buildOutput = await buildProject(\n        xcodeProject,\n        platform,\n        selectedDevice.udid,\n        mode,\n        scheme,\n        args,\n      );\n    }\n\n    logger.info(`Installing and launching your app on ${selectedDevice.name}`);\n\n    installApp({\n      buildOutput: buildOutput ?? '',\n      xcodeProject,\n      mode,\n      scheme,\n      target,\n      udid: selectedDevice.udid,\n      binaryPath,\n      isSimulator: false,\n    });\n  }\n\n  return logger.success('Installed the app on the device.');\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAEA;AACA;AAAsC;AAE/B,eAAeA,WAAW,CAC/BC,cAAsB,EACtBC,QAAuB,EACvBC,IAAY,EACZC,MAAc,EACdC,YAA4B,EAC5BC,IAAY,EACZ;EACA,IAAIA,IAAI,CAACC,UAAU,IAAIN,cAAc,CAACO,IAAI,KAAK,UAAU,EAAE;IACzD,MAAM,KAAIC,oBAAQ,EAChB,wEAAwE,CACzE;EACH;EAEA,IAAIR,cAAc,CAACO,IAAI,KAAK,UAAU,EAAE;IACtC,MAAME,WAAW,GAAG,MAAM,IAAAC,0BAAY,EACpCN,YAAY,EACZH,QAAQ,EACRD,cAAc,CAACW,IAAI,EACnBT,IAAI,EACJC,MAAM,EACNE,IAAI,CACL;IAED,MAAMO,aAAa,GAAG,MAAM,IAAAC,kCAAgB,EAC1CT,YAAY,EACZF,IAAI,EACJO,WAAW,EACXN,MAAM,CACP;IAED,IAAI,CAACS,aAAa,EAAE;MAClB,MAAM,KAAIJ,oBAAQ,EAAC,+CAA+C,CAAC;IACrE;IAEA,MAAMM,OAAO,GAAG,MAAM,IAAAC,0BAAY,EAACH,aAAa,EAAEX,QAAQ,EAAE,IAAI,CAAC;IACjE,MAAMe,UAAU,GAAGC,wBAAa,CAACC,KAAK,CAAE,GAAEJ,OAAQ,IAAGX,MAAO,EAAC,EAAE,EAAE,EAAE;MACjEgB,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT,CAAC,CAAC;IACFJ,UAAU,CAACK,KAAK,EAAE;EACpB,CAAC,MAAM;IACL,MAAM;MAACf,UAAU;MAAEgB;IAAM,CAAC,GAAGjB,IAAI;IAEjC,IAAII,WAAW;IACf,IAAI,CAACH,UAAU,EAAE;MACfG,WAAW,GAAG,MAAM,IAAAC,0BAAY,EAC9BN,YAAY,EACZH,QAAQ,EACRD,cAAc,CAACW,IAAI,EACnBT,IAAI,EACJC,MAAM,EACNE,IAAI,CACL;IACH;IAEAkB,kBAAM,CAACC,IAAI,CAAE,wCAAuCxB,cAAc,CAACyB,IAAK,EAAC,CAAC;IAE1E,IAAAC,mBAAU,EAAC;MACTjB,WAAW,EAAEA,WAAW,IAAI,EAAE;MAC9BL,YAAY;MACZF,IAAI;MACJC,MAAM;MACNmB,MAAM;MACNX,IAAI,EAAEX,cAAc,CAACW,IAAI;MACzBL,UAAU;MACVqB,WAAW,EAAE;IACf,CAAC,CAAC;EACJ;EAEA,OAAOJ,kBAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;AAC3D"}