{"version": 3, "names": ["getRunOptions", "platformName", "readableName", "getPlatformInfo", "isMac", "name", "description", "default", "process", "env", "RCT_METRO_PORT", "parse", "Number", "getDefaultUserTerminal", "getBuildOptions"], "sources": ["../../../src/commands/runCommand/runOptions.ts"], "sourcesContent": ["import {getDefaultUserTerminal} from '@react-native-community/cli-tools';\nimport {BuilderCommand} from '../../types';\nimport {getPlatformInfo} from './getPlatformInfo';\nimport {getBuildOptions} from '../buildCommand/buildOptions';\n\nexport const getRunOptions = ({platformName}: BuilderCommand) => {\n  const {readableName} = getPlatformInfo(platformName);\n  const isMac = platformName === 'macos';\n  return [\n    {\n      name: '--no-packager',\n      description: 'Do not launch packager while running the app',\n    },\n    {\n      name: '--port <number>',\n      default: process.env.RCT_METRO_PORT || 8081,\n      parse: Number,\n    },\n    {\n      name: '--terminal <string>',\n      description:\n        'Launches the Metro Bundler in a new window using the specified terminal path.',\n      default: getDefaultUserTerminal(),\n    },\n    {\n      name: '--binary-path <string>',\n      description:\n        'Path relative to project root where pre-built .app binary lives.',\n    },\n    {\n      name: '--list-devices',\n      description: `List all available ${readableName} devices and simulators and let you choose one to run the app. `,\n    },\n    {\n      name: '--udid <string>',\n      description: 'Explicitly set the device to use by UDID',\n    },\n    !isMac && {\n      name: '--simulator <string>',\n      description:\n        `Explicitly set the simulator to use. Optionally set the ${readableName} version ` +\n        'between parentheses at the end to match an exact version: ' +\n        '\"iPhone 15 (17.0)\"',\n    },\n    ...getBuildOptions({platformName}),\n  ];\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAEO,MAAMA,aAAa,GAAG,CAAC;EAACC;AAA4B,CAAC,KAAK;EAC/D,MAAM;IAACC;EAAY,CAAC,GAAG,IAAAC,gCAAe,EAACF,YAAY,CAAC;EACpD,MAAMG,KAAK,GAAGH,YAAY,KAAK,OAAO;EACtC,OAAO,CACL;IACEI,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBE,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAI,IAAI;IAC3CC,KAAK,EAAEC;EACT,CAAC,EACD;IACEP,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT,+EAA+E;IACjFC,OAAO,EAAE,IAAAM,kCAAsB;EACjC,CAAC,EACD;IACER,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAG,sBAAqBJ,YAAa;EAClD,CAAC,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD,CAACF,KAAK,IAAI;IACRC,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EACR,2DAA0DJ,YAAa,WAAU,GAClF,4DAA4D,GAC5D;EACJ,CAAC,EACD,GAAG,IAAAY,6BAAe,EAAC;IAACb;EAAY,CAAC,CAAC,CACnC;AACH,CAAC;AAAC"}