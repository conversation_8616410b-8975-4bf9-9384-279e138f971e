{"version": 3, "names": ["getBuildPath", "buildSettings", "platform", "isCatalyst", "targetBuildDir", "TARGET_BUILD_DIR", "executableFolderPath", "EXECUTABLE_FOLDER_PATH", "fullProductName", "FULL_PRODUCT_NAME", "CLIError", "buildPath", "path", "join", "match", "fs", "existsSync"], "sources": ["../../../src/commands/runCommand/getBuildPath.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport path from 'path';\nimport fs from 'fs';\nimport {BuildSettings} from './getBuildSettings';\nimport {ApplePlatform} from '../../types';\n\nexport async function getBuildPath(\n  buildSettings: BuildSettings,\n  platform: ApplePlatform = 'ios',\n  isCatalyst: boolean = false,\n) {\n  let targetBuildDir = buildSettings.TARGET_BUILD_DIR;\n  const executableFolderPath = buildSettings.EXECUTABLE_FOLDER_PATH;\n  const fullProductName = buildSettings.FULL_PRODUCT_NAME;\n\n  if (!targetBuildDir) {\n    throw new CLIError('Failed to get the target build directory.');\n  }\n\n  if (!executableFolderPath) {\n    throw new CLIError('Failed to get the app name.');\n  }\n\n  if (!fullProductName) {\n    throw new CLIError('Failed to get product name.');\n  }\n\n  // Default is platform == ios && isCatalyst == false\n  let buildPath = path.join(targetBuildDir, executableFolderPath);\n\n  // platform == ios && isCatalyst == true needs build path suffix,\n  // but this regresses from time to time with suffix present or not\n  // so check - there may be one already, or we may need to add suffix\n  if (platform === 'ios' && isCatalyst) {\n    // make sure path has one and only one '-maccatalyst' suffix on end\n    if (!targetBuildDir.match(/-maccatalyst$/)) {\n      targetBuildDir = `${targetBuildDir}-maccatalyst`;\n    }\n    buildPath = path.join(targetBuildDir, executableFolderPath);\n  }\n\n  // macOS gets the product name, not the executable folder path\n  if (platform === 'macos') {\n    buildPath = path.join(targetBuildDir, fullProductName);\n  }\n\n  // Make sure the directory exists and fail fast vs silently failing\n  if (!fs.existsSync(targetBuildDir)) {\n    throw new CLIError(\n      `target build directory ${targetBuildDir} does not exist`,\n    );\n  }\n\n  return buildPath;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAIb,eAAeA,YAAY,CAChCC,aAA4B,EAC5BC,QAAuB,GAAG,KAAK,EAC/BC,UAAmB,GAAG,KAAK,EAC3B;EACA,IAAIC,cAAc,GAAGH,aAAa,CAACI,gBAAgB;EACnD,MAAMC,oBAAoB,GAAGL,aAAa,CAACM,sBAAsB;EACjE,MAAMC,eAAe,GAAGP,aAAa,CAACQ,iBAAiB;EAEvD,IAAI,CAACL,cAAc,EAAE;IACnB,MAAM,KAAIM,oBAAQ,EAAC,2CAA2C,CAAC;EACjE;EAEA,IAAI,CAACJ,oBAAoB,EAAE;IACzB,MAAM,KAAII,oBAAQ,EAAC,6BAA6B,CAAC;EACnD;EAEA,IAAI,CAACF,eAAe,EAAE;IACpB,MAAM,KAAIE,oBAAQ,EAAC,6BAA6B,CAAC;EACnD;;EAEA;EACA,IAAIC,SAAS,GAAGC,eAAI,CAACC,IAAI,CAACT,cAAc,EAAEE,oBAAoB,CAAC;;EAE/D;EACA;EACA;EACA,IAAIJ,QAAQ,KAAK,KAAK,IAAIC,UAAU,EAAE;IACpC;IACA,IAAI,CAACC,cAAc,CAACU,KAAK,CAAC,eAAe,CAAC,EAAE;MAC1CV,cAAc,GAAI,GAAEA,cAAe,cAAa;IAClD;IACAO,SAAS,GAAGC,eAAI,CAACC,IAAI,CAACT,cAAc,EAAEE,oBAAoB,CAAC;EAC7D;;EAEA;EACA,IAAIJ,QAAQ,KAAK,OAAO,EAAE;IACxBS,SAAS,GAAGC,eAAI,CAACC,IAAI,CAACT,cAAc,EAAEI,eAAe,CAAC;EACxD;;EAEA;EACA,IAAI,CAACO,aAAE,CAACC,UAAU,CAACZ,cAAc,CAAC,EAAE;IAClC,MAAM,KAAIM,oBAAQ,EACf,0BAAyBN,cAAe,iBAAgB,CAC1D;EACH;EAEA,OAAOO,SAAS;AAClB"}