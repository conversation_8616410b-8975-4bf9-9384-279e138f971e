{"version": 3, "names": ["matchingDevice", "devices", "deviceName", "firstIOSDevice", "find", "d", "type", "logger", "info", "chalk", "bold", "name", "error", "undefined", "device", "formattedDeviceName", "simulator", "version", "printFoundDevices", "filteredDevice", "filter", "map", "udid", "join"], "sources": ["../../../src/commands/runCommand/matchingDevice.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport {Device, DeviceType} from '../../types';\n\nexport function matchingDevice(\n  devices: Array<Device>,\n  deviceName: string | true | undefined,\n) {\n  // The condition specifically checks if the value is `true`, not just truthy to allow for `--device` flag without a value\n  if (deviceName === true) {\n    const firstIOSDevice = devices.find((d) => d.type === 'device')!;\n    if (firstIOSDevice) {\n      logger.info(\n        `Using first available device named \"${chalk.bold(\n          firstIOSDevice.name,\n        )}\" due to lack of name supplied.`,\n      );\n      return firstIOSDevice;\n    } else {\n      logger.error('No iOS devices connected.');\n      return undefined;\n    }\n  }\n  return devices.find(\n    (device) =>\n      device.name === deviceName || formattedDeviceName(device) === deviceName,\n  );\n}\n\nexport function formattedDeviceName(simulator: Device) {\n  return simulator.version\n    ? `${simulator.name} (${simulator.version})`\n    : simulator.name;\n}\n\nexport function printFoundDevices(devices: Array<Device>, type?: DeviceType) {\n  let filteredDevice = [...devices];\n\n  if (type) {\n    filteredDevice = filteredDevice.filter((device) => device.type === type);\n  }\n\n  return [\n    'Available devices:',\n    ...filteredDevice.map(({name, udid}) => `  - ${name} (${udid})`),\n  ].join('\\n');\n}\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAGnB,SAASA,cAAc,CAC5BC,OAAsB,EACtBC,UAAqC,EACrC;EACA;EACA,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,MAAMC,cAAc,GAAGF,OAAO,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAK,QAAQ,CAAE;IAChE,IAAIH,cAAc,EAAE;MAClBI,kBAAM,CAACC,IAAI,CACR,uCAAsCC,gBAAK,CAACC,IAAI,CAC/CP,cAAc,CAACQ,IAAI,CACnB,iCAAgC,CACnC;MACD,OAAOR,cAAc;IACvB,CAAC,MAAM;MACLI,kBAAM,CAACK,KAAK,CAAC,2BAA2B,CAAC;MACzC,OAAOC,SAAS;IAClB;EACF;EACA,OAAOZ,OAAO,CAACG,IAAI,CAChBU,MAAM,IACLA,MAAM,CAACH,IAAI,KAAKT,UAAU,IAAIa,mBAAmB,CAACD,MAAM,CAAC,KAAKZ,UAAU,CAC3E;AACH;AAEO,SAASa,mBAAmB,CAACC,SAAiB,EAAE;EACrD,OAAOA,SAAS,CAACC,OAAO,GACnB,GAAED,SAAS,CAACL,IAAK,KAAIK,SAAS,CAACC,OAAQ,GAAE,GAC1CD,SAAS,CAACL,IAAI;AACpB;AAEO,SAASO,iBAAiB,CAACjB,OAAsB,EAAEK,IAAiB,EAAE;EAC3E,IAAIa,cAAc,GAAG,CAAC,GAAGlB,OAAO,CAAC;EAEjC,IAAIK,IAAI,EAAE;IACRa,cAAc,GAAGA,cAAc,CAACC,MAAM,CAAEN,MAAM,IAAKA,MAAM,CAACR,IAAI,KAAKA,IAAI,CAAC;EAC1E;EAEA,OAAO,CACL,oBAAoB,EACpB,GAAGa,cAAc,CAACE,GAAG,CAAC,CAAC;IAACV,IAAI;IAAEW;EAAI,CAAC,KAAM,OAAMX,IAAK,KAAIW,IAAK,GAAE,CAAC,CACjE,CAACC,IAAI,CAAC,IAAI,CAAC;AACd"}