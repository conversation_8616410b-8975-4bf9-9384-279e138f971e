{"version": 3, "names": ["getLogOptions", "name", "description"], "sources": ["../../../src/commands/logCommand/logOptions.ts"], "sourcesContent": ["import {BuilderCommand} from '../../types';\n\nexport const getLogOptions = ({}: BuilderCommand) => [\n  {\n    name: '-i --interactive',\n    description:\n      'Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator.',\n  },\n];\n"], "mappings": ";;;;;;AAEO,MAAMA,aAAa,GAAG,CAAC,CAAiB,CAAC,KAAK,CACnD;EACEC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EACT;AACJ,CAAC,CACF;AAAC"}