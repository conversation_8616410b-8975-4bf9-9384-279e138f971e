{"version": 3, "names": ["simulatorDestinationMap", "ios", "macos", "visionos", "tvos"], "sources": ["../../../src/commands/buildCommand/simulatorDestinationMap.ts"], "sourcesContent": ["import {ApplePlatform} from '../../types';\n\nexport const simulatorDestinationMap: Record<ApplePlatform, string> = {\n  ios: 'iOS Simulator',\n  macos: 'macOS',\n  visionos: 'visionOS Simulator',\n  tvos: 'tvOS Simulator',\n};\n"], "mappings": ";;;;;;AAEO,MAAMA,uBAAsD,GAAG;EACpEC,GAAG,EAAE,eAAe;EACpBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,oBAAoB;EAC9BC,IAAI,EAAE;AACR,CAAC;AAAC"}