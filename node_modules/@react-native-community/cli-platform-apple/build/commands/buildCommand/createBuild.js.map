{"version": 3, "names": ["createBuild", "platformName", "_", "ctx", "args", "platformConfig", "project", "undefined", "supportedPlatforms", "CLIError", "installedPods", "automaticPodsInstallation", "forcePods", "isAppRunningNewArchitecture", "sourceDir", "getArchitecture", "resolvePods", "root", "dependencies", "forceInstall", "newArchEnabled", "xcodeProject", "getXcodeProjectAndDir", "process", "chdir", "scheme", "mode", "getConfiguration", "buildProject"], "sources": ["../../../src/commands/buildCommand/createBuild.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport {Config, IOSProjectConfig} from '@react-native-community/cli-types';\nimport getArchitecture from '../../tools/getArchitecture';\nimport {BuildFlags} from './buildOptions';\nimport {buildProject} from './buildProject';\nimport {getConfiguration} from './getConfiguration';\nimport {getXcodeProjectAndDir} from './getXcodeProjectAndDir';\nimport {BuilderCommand} from '../../types';\nimport {\n  supportedPlatforms,\n  resolvePods,\n} from '@react-native-community/cli-config-apple';\n\nconst createBuild =\n  ({platformName}: BuilderCommand) =>\n  async (_: Array<string>, ctx: Config, args: BuildFlags) => {\n    const platformConfig = ctx.project[platformName] as IOSProjectConfig;\n\n    if (\n      platformConfig === undefined ||\n      supportedPlatforms[platformName] === undefined\n    ) {\n      throw new CLIError(`Unable to find ${platformName} platform config`);\n    }\n\n    let installedPods = false;\n    if (platformConfig.automaticPodsInstallation || args.forcePods) {\n      const isAppRunningNewArchitecture = platformConfig.sourceDir\n        ? await getArchitecture(platformConfig.sourceDir)\n        : undefined;\n\n      await resolvePods(\n        ctx.root,\n        platformConfig.sourceDir,\n        ctx.dependencies,\n        platformName,\n        {\n          forceInstall: args.forcePods,\n          newArchEnabled: isAppRunningNewArchitecture,\n        },\n      );\n\n      installedPods = true;\n    }\n\n    let {xcodeProject, sourceDir} = getXcodeProjectAndDir(\n      platformConfig,\n      platformName,\n      installedPods,\n    );\n\n    process.chdir(sourceDir);\n\n    const {scheme, mode} = await getConfiguration(\n      xcodeProject,\n      sourceDir,\n      args,\n      platformName,\n    );\n\n    return buildProject(\n      xcodeProject,\n      platformName,\n      undefined,\n      mode,\n      scheme,\n      args,\n    );\n  };\n\nexport default createBuild;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAEA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGkD;AAElD,MAAMA,WAAW,GACf,CAAC;EAACC;AAA4B,CAAC,KAC/B,OAAOC,CAAgB,EAAEC,GAAW,EAAEC,IAAgB,KAAK;EACzD,MAAMC,cAAc,GAAGF,GAAG,CAACG,OAAO,CAACL,YAAY,CAAqB;EAEpE,IACEI,cAAc,KAAKE,SAAS,IAC5BC,oCAAkB,CAACP,YAAY,CAAC,KAAKM,SAAS,EAC9C;IACA,MAAM,KAAIE,oBAAQ,EAAE,kBAAiBR,YAAa,kBAAiB,CAAC;EACtE;EAEA,IAAIS,aAAa,GAAG,KAAK;EACzB,IAAIL,cAAc,CAACM,yBAAyB,IAAIP,IAAI,CAACQ,SAAS,EAAE;IAC9D,MAAMC,2BAA2B,GAAGR,cAAc,CAACS,SAAS,GACxD,MAAM,IAAAC,wBAAe,EAACV,cAAc,CAACS,SAAS,CAAC,GAC/CP,SAAS;IAEb,MAAM,IAAAS,6BAAW,EACfb,GAAG,CAACc,IAAI,EACRZ,cAAc,CAACS,SAAS,EACxBX,GAAG,CAACe,YAAY,EAChBjB,YAAY,EACZ;MACEkB,YAAY,EAAEf,IAAI,CAACQ,SAAS;MAC5BQ,cAAc,EAAEP;IAClB,CAAC,CACF;IAEDH,aAAa,GAAG,IAAI;EACtB;EAEA,IAAI;IAACW,YAAY;IAAEP;EAAS,CAAC,GAAG,IAAAQ,4CAAqB,EACnDjB,cAAc,EACdJ,YAAY,EACZS,aAAa,CACd;EAEDa,OAAO,CAACC,KAAK,CAACV,SAAS,CAAC;EAExB,MAAM;IAACW,MAAM;IAAEC;EAAI,CAAC,GAAG,MAAM,IAAAC,kCAAgB,EAC3CN,YAAY,EACZP,SAAS,EACTV,IAAI,EACJH,YAAY,CACb;EAED,OAAO,IAAA2B,0BAAY,EACjBP,YAAY,EACZpB,YAAY,EACZM,SAAS,EACTmB,IAAI,EACJD,MAAM,EACNrB,IAAI,CACL;AACH,CAAC;AAAC,eAEWJ,WAAW;AAAA"}