{"version": 3, "names": ["getXcodeProjectAndDir", "iosProjectConfig", "platformName", "installedPods", "readableName", "platformReadableName", "getPlatformInfo", "CLIError", "xcodeProject", "sourceDir", "recheckXcodeProject", "findXcodeProject", "fs", "readdirSync"], "sources": ["../../../src/commands/buildCommand/getXcodeProjectAndDir.ts"], "sourcesContent": ["import fs from 'fs';\nimport {IOSProjectConfig} from '@react-native-community/cli-types';\nimport {CLIError} from '@react-native-community/cli-tools';\nimport {findXcodeProject} from '@react-native-community/cli-config-apple';\nimport {getPlatformInfo} from '../runCommand/getPlatformInfo';\nimport {ApplePlatform} from '../../types';\n\nexport function getXcodeProjectAndDir(\n  iosProjectConfig: IOSProjectConfig | undefined,\n  platformName: ApplePlatform,\n  installedPods?: boolean,\n) {\n  const {readableName: platformReadableName} = getPlatformInfo(platformName);\n\n  if (!iosProjectConfig) {\n    throw new CLIError(\n      `${platformReadableName} project folder not found. Make sure that project.${platformName}.sourceDir points to a directory with your Xcode project and that you are running this command inside of React Native project.`,\n    );\n  }\n\n  let {xcodeProject, sourceDir} = iosProjectConfig;\n\n  if (!xcodeProject) {\n    throw new CLIError(\n      `Could not find Xcode project files in \"${sourceDir}\" folder. Please make sure that you have installed Cocoapods and \"${sourceDir}\" is a valid path`,\n    );\n  }\n\n  // if project is freshly created, revisit Xcode project to verify Pods are installed correctly.\n  // This is needed because ctx project is created before Pods are installed, so it might have outdated information.\n  if (installedPods) {\n    const recheckXcodeProject = findXcodeProject(fs.readdirSync(sourceDir));\n    if (recheckXcodeProject) {\n      xcodeProject = recheckXcodeProject;\n    }\n  }\n\n  return {xcodeProject, sourceDir};\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA8D;AAGvD,SAASA,qBAAqB,CACnCC,gBAA8C,EAC9CC,YAA2B,EAC3BC,aAAuB,EACvB;EACA,MAAM;IAACC,YAAY,EAAEC;EAAoB,CAAC,GAAG,IAAAC,gCAAe,EAACJ,YAAY,CAAC;EAE1E,IAAI,CAACD,gBAAgB,EAAE;IACrB,MAAM,KAAIM,oBAAQ,EACf,GAAEF,oBAAqB,qDAAoDH,YAAa,gIAA+H,CACzN;EACH;EAEA,IAAI;IAACM,YAAY;IAAEC;EAAS,CAAC,GAAGR,gBAAgB;EAEhD,IAAI,CAACO,YAAY,EAAE;IACjB,MAAM,KAAID,oBAAQ,EACf,0CAAyCE,SAAU,qEAAoEA,SAAU,mBAAkB,CACrJ;EACH;;EAEA;EACA;EACA,IAAIN,aAAa,EAAE;IACjB,MAAMO,mBAAmB,GAAG,IAAAC,kCAAgB,EAACC,aAAE,CAACC,WAAW,CAACJ,SAAS,CAAC,CAAC;IACvE,IAAIC,mBAAmB,EAAE;MACvBF,YAAY,GAAGE,mBAAmB;IACpC;EACF;EAEA,OAAO;IAACF,YAAY;IAAEC;EAAS,CAAC;AAClC"}