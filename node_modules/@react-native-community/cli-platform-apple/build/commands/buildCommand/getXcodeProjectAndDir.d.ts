import { IOSProjectConfig } from '@react-native-community/cli-types';
import { ApplePlatform } from '../../types';
export declare function getXcodeProjectAndDir(iosProjectConfig: IOSProjectConfig | undefined, platformName: ApplePlatform, installedPods?: boolean): {
    xcodeProject: import("@react-native-community/cli-types").IOSProjectInfo;
    sourceDir: string;
};
//# sourceMappingURL=getXcodeProjectAndDir.d.ts.map