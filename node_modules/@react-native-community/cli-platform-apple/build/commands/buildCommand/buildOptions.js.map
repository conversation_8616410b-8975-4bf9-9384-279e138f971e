{"version": 3, "names": ["getBuildOptions", "platformName", "readableName", "getPlatformInfo", "isMac", "name", "description", "parse", "val", "split"], "sources": ["../../../src/commands/buildCommand/buildOptions.ts"], "sourcesContent": ["import {BuilderCommand} from '../../types';\nimport {getPlatformInfo} from '../runCommand/getPlatformInfo';\n\nexport type BuildFlags = {\n  mode?: string;\n  target?: string;\n  verbose?: boolean;\n  scheme?: string;\n  xcconfig?: string;\n  buildFolder?: string;\n  interactive?: boolean;\n  destination?: string;\n  extraParams?: string[];\n  forcePods?: boolean;\n};\n\nexport const getBuildOptions = ({platformName}: BuilderCommand) => {\n  const {readableName} = getPlatformInfo(platformName);\n  const isMac = platformName === 'macos';\n\n  return [\n    {\n      name: '--mode <string>',\n      description:\n        'Explicitly set the scheme configuration to use. This option is case sensitive.',\n    },\n    {\n      name: '--scheme <string>',\n      description: 'Explicitly set Xcode scheme to use',\n    },\n    {\n      name: '--destination <string>',\n      description: 'Explicitly extend destination e.g. \"arch=x86_64\"',\n    },\n    {\n      name: '--verbose',\n      description: 'Do not use xcbeautify or xcpretty even if installed',\n    },\n    {\n      name: '--xcconfig [string]',\n      description: 'Explicitly set xcconfig to use',\n    },\n    {\n      name: '--buildFolder <string>',\n      description: `Location for ${readableName} build artifacts. Corresponds to Xcode's \"-derivedDataPath\".`,\n    },\n    {\n      name: '--extra-params <string>',\n      description: 'Custom params that will be passed to xcodebuild command.',\n      parse: (val: string) => val.split(' '),\n    },\n    {\n      name: '--target <string>',\n      description: 'Explicitly set Xcode target to use.',\n    },\n    {\n      name: '-i --interactive',\n      description:\n        'Explicitly select which scheme and configuration to use before running a build',\n    },\n    {\n      name: '--force-pods',\n      description: 'Force CocoaPods installation',\n    },\n    !isMac && {\n      name: '--device [string]', // here we're intentionally using [] over <> to make passed value optional to allow users to run only on physical devices\n      description:\n        'Explicitly set the device to use by name or by unique device identifier . If the value is not provided,' +\n        'the app will run on the first available physical device.',\n    },\n  ];\n};\n"], "mappings": ";;;;;;AACA;AAeO,MAAMA,eAAe,GAAG,CAAC;EAACC;AAA4B,CAAC,KAAK;EACjE,MAAM;IAACC;EAAY,CAAC,GAAG,IAAAC,gCAAe,EAACF,YAAY,CAAC;EACpD,MAAMG,KAAK,GAAGH,YAAY,KAAK,OAAO;EAEtC,OAAO,CACL;IACEI,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAG,gBAAeJ,YAAa;EAC5C,CAAC,EACD;IACEG,IAAI,EAAE,yBAAyB;IAC/BC,WAAW,EAAE,0DAA0D;IACvEC,KAAK,EAAGC,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;EACvC,CAAC,EACD;IACEJ,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE;EACf,CAAC,EACD,CAACF,KAAK,IAAI;IACRC,IAAI,EAAE,mBAAmB;IAAE;IAC3BC,WAAW,EACT,yGAAyG,GACzG;EACJ,CAAC,CACF;AACH,CAAC;AAAC"}