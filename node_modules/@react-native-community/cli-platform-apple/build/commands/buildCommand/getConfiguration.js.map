{"version": 3, "names": ["getConfiguration", "xcodeProject", "sourceDir", "args", "platformName", "info", "getInfo", "mode", "checkIfConfigurationExists", "configurations", "scheme", "path", "basename", "name", "extname", "schemes", "includes", "readableName", "getPlatformInfo", "fallbackScheme", "logger", "warn", "chalk", "bold", "getBuildConfigurationFromXcScheme", "interactive", "selection", "selectFromInteractiveMode", "isWorkspace"], "sources": ["../../../src/commands/buildCommand/getConfiguration.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport type {IOSProjectInfo} from '@react-native-community/cli-types';\nimport {logger} from '@react-native-community/cli-tools';\nimport {selectFromInteractiveMode} from '../../tools/selectFromInteractiveMode';\nimport {getInfo} from '../../tools/getInfo';\nimport {checkIfConfigurationExists} from '../../tools/checkIfConfigurationExists';\nimport type {BuildFlags} from './buildOptions';\nimport {getBuildConfigurationFromXcScheme} from '../../tools/getBuildConfigurationFromXcScheme';\nimport path from 'path';\nimport {getPlatformInfo} from '../runCommand/getPlatformInfo';\nimport {ApplePlatform} from '../../types';\n\nexport async function getConfiguration(\n  xcodeProject: IOSProjectInfo,\n  sourceDir: string,\n  args: BuildFlags,\n  platformName: ApplePlatform,\n) {\n  const info = getInfo(xcodeProject, sourceDir);\n\n  if (args.mode) {\n    checkIfConfigurationExists(info?.configurations ?? [], args.mode);\n  }\n\n  let scheme =\n    args.scheme ||\n    path.basename(xcodeProject.name, path.extname(xcodeProject.name));\n\n  if (!info?.schemes?.includes(scheme)) {\n    const {readableName} = getPlatformInfo(platformName);\n    const fallbackScheme = `${scheme}-${readableName}`;\n\n    if (info?.schemes?.includes(fallbackScheme)) {\n      logger.warn(\n        `Scheme \"${chalk.bold(\n          scheme,\n        )}\" doesn't exist. Using fallback scheme \"${chalk.bold(\n          fallbackScheme,\n        )}\"`,\n      );\n\n      scheme = fallbackScheme;\n    }\n  }\n\n  let mode =\n    args.mode ||\n    getBuildConfigurationFromXcScheme(scheme, 'Debug', sourceDir, info);\n\n  if (args.interactive) {\n    const selection = await selectFromInteractiveMode({\n      scheme,\n      mode,\n      info,\n    });\n\n    if (selection.scheme) {\n      scheme = selection.scheme;\n    }\n\n    if (selection.mode) {\n      mode = selection.mode;\n    }\n  }\n\n  logger.info(\n    `Found Xcode ${\n      xcodeProject.isWorkspace ? 'workspace' : 'project'\n    } \"${chalk.bold(xcodeProject.name)}\"`,\n  );\n\n  return {scheme, mode};\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA8D;AAGvD,eAAeA,gBAAgB,CACpCC,YAA4B,EAC5BC,SAAiB,EACjBC,IAAgB,EAChBC,YAA2B,EAC3B;EAAA;EACA,MAAMC,IAAI,GAAG,IAAAC,gBAAO,EAACL,YAAY,EAAEC,SAAS,CAAC;EAE7C,IAAIC,IAAI,CAACI,IAAI,EAAE;IACb,IAAAC,sDAA0B,EAAC,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,cAAc,KAAI,EAAE,EAAEN,IAAI,CAACI,IAAI,CAAC;EACnE;EAEA,IAAIG,MAAM,GACRP,IAAI,CAACO,MAAM,IACXC,eAAI,CAACC,QAAQ,CAACX,YAAY,CAACY,IAAI,EAAEF,eAAI,CAACG,OAAO,CAACb,YAAY,CAACY,IAAI,CAAC,CAAC;EAEnE,IAAI,EAACR,IAAI,aAAJA,IAAI,wCAAJA,IAAI,CAAEU,OAAO,kDAAb,cAAeC,QAAQ,CAACN,MAAM,CAAC,GAAE;IAAA;IACpC,MAAM;MAACO;IAAY,CAAC,GAAG,IAAAC,gCAAe,EAACd,YAAY,CAAC;IACpD,MAAMe,cAAc,GAAI,GAAET,MAAO,IAAGO,YAAa,EAAC;IAElD,IAAIZ,IAAI,aAAJA,IAAI,yCAAJA,IAAI,CAAEU,OAAO,mDAAb,eAAeC,QAAQ,CAACG,cAAc,CAAC,EAAE;MAC3CC,kBAAM,CAACC,IAAI,CACR,WAAUC,gBAAK,CAACC,IAAI,CACnBb,MAAM,CACN,2CAA0CY,gBAAK,CAACC,IAAI,CACpDJ,cAAc,CACd,GAAE,CACL;MAEDT,MAAM,GAAGS,cAAc;IACzB;EACF;EAEA,IAAIZ,IAAI,GACNJ,IAAI,CAACI,IAAI,IACT,IAAAiB,oEAAiC,EAACd,MAAM,EAAE,OAAO,EAAER,SAAS,EAAEG,IAAI,CAAC;EAErE,IAAIF,IAAI,CAACsB,WAAW,EAAE;IACpB,MAAMC,SAAS,GAAG,MAAM,IAAAC,oDAAyB,EAAC;MAChDjB,MAAM;MACNH,IAAI;MACJF;IACF,CAAC,CAAC;IAEF,IAAIqB,SAAS,CAAChB,MAAM,EAAE;MACpBA,MAAM,GAAGgB,SAAS,CAAChB,MAAM;IAC3B;IAEA,IAAIgB,SAAS,CAACnB,IAAI,EAAE;MAClBA,IAAI,GAAGmB,SAAS,CAACnB,IAAI;IACvB;EACF;EAEAa,kBAAM,CAACf,IAAI,CACR,eACCJ,YAAY,CAAC2B,WAAW,GAAG,WAAW,GAAG,SAC1C,KAAIN,gBAAK,CAACC,IAAI,CAACtB,YAAY,CAACY,IAAI,CAAE,GAAE,CACtC;EAED,OAAO;IAACH,MAAM;IAAEH;EAAI,CAAC;AACvB"}