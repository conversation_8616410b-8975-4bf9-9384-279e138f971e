{"version": 3, "names": ["prettifyXcodebuildMessages", "output", "errorRegex", "errors", "Set", "match", "exec", "add", "trim", "buildProject", "xcodeProject", "platform", "udid", "mode", "scheme", "args", "Promise", "resolve", "reject", "simulatorDest", "simulatorDestinationMap", "CLIError", "Object", "values", "supportedPlatforms", "join", "xcodebuildArgs", "isWorkspace", "name", "xcconfig", "buildFolder", "destination", "extraParams", "push", "loader", "<PERSON><PERSON><PERSON><PERSON>", "logger", "info", "chalk", "dim", "xcodebuildOutputFormatter", "verbose", "xcbeautifyAvailable", "child_process", "spawn", "stdio", "process", "stdout", "stderr", "xcprettyAvailable", "buildProcess", "getProcessOptions", "buildOutput", "on", "data", "stringData", "toString", "stdin", "write", "isVerbose", "debug", "start", "repeat", "length", "code", "end", "stop", "printRunDoctorTip", "Array", "from", "for<PERSON>ach", "error", "success", "execSync", "packager", "terminal", "port", "String", "env", "RCT_TERMINAL", "RCT_METRO_PORT"], "sources": ["../../../src/commands/buildCommand/buildProject.ts"], "sourcesContent": ["import child_process, {\n  ChildProcess,\n  SpawnOptionsWithoutStdio,\n} from 'child_process';\nimport chalk from 'chalk';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\nimport {\n  logger,\n  CLIError,\n  printRunDoctorTip,\n  getLoader,\n} from '@react-native-community/cli-tools';\nimport type {BuildFlags} from './buildOptions';\nimport {simulatorDestinationMap} from './simulatorDestinationMap';\nimport {supportedPlatforms} from '@react-native-community/cli-config-apple';\nimport {ApplePlatform} from '../../types';\n\nfunction prettifyXcodebuildMessages(output: string): Set<string> {\n  const errorRegex = /error\\b[^\\S\\r\\n]*[:\\-\\s]*([^\\r\\n]*)/gim;\n  const errors = new Set<string>();\n\n  let match;\n  while ((match = errorRegex.exec(output)) !== null) {\n    if (match[1]) {\n      // match[1] contains the captured group that excludes any leading colons or spaces\n      errors.add(match[1].trim());\n    }\n  }\n\n  return errors;\n}\n\nexport function buildProject(\n  xcodeProject: IOSProjectInfo,\n  platform: ApplePlatform,\n  udid: string | undefined,\n  mode: string,\n  scheme: string,\n  args: BuildFlags,\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const simulatorDest = simulatorDestinationMap?.[platform];\n\n    if (!simulatorDest) {\n      reject(\n        new CLIError(\n          `Unknown platform: ${platform}. Please, use one of: ${Object.values(\n            supportedPlatforms,\n          ).join(', ')}.`,\n        ),\n      );\n      return;\n    }\n\n    const xcodebuildArgs = [\n      xcodeProject.isWorkspace ? '-workspace' : '-project',\n      xcodeProject.name,\n      ...(args.xcconfig ? ['-xcconfig', args.xcconfig] : []),\n      ...(args.buildFolder ? ['-derivedDataPath', args.buildFolder] : []),\n      '-configuration',\n      mode,\n      '-scheme',\n      scheme,\n      '-destination',\n      (udid\n        ? `id=${udid}`\n        : mode === 'Debug'\n        ? `generic/platform=${simulatorDest}`\n        : `generic/platform=${platform}`) +\n        (args.destination ? ',' + args.destination : ''),\n    ];\n\n    if (args.extraParams) {\n      xcodebuildArgs.push(...args.extraParams);\n    }\n\n    const loader = getLoader();\n    logger.info(\n      `Building ${chalk.dim(\n        `(using \"xcodebuild ${xcodebuildArgs.join(' ')}\")`,\n      )}`,\n    );\n    let xcodebuildOutputFormatter: ChildProcess | any;\n    if (!args.verbose) {\n      if (xcbeautifyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcbeautify', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      } else if (xcprettyAvailable()) {\n        xcodebuildOutputFormatter = child_process.spawn('xcpretty', [], {\n          stdio: ['pipe', process.stdout, process.stderr],\n        });\n      }\n    }\n\n    const buildProcess = child_process.spawn(\n      'xcodebuild',\n      xcodebuildArgs,\n      getProcessOptions(args),\n    );\n    let buildOutput = '';\n    buildProcess.stdout.on('data', (data: Buffer) => {\n      const stringData = data.toString();\n      buildOutput += stringData;\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.write(data);\n      } else {\n        if (logger.isVerbose()) {\n          logger.debug(stringData);\n        } else {\n          loader.start(\n            `Building the app${'.'.repeat(buildOutput.length % 10)}`,\n          );\n        }\n      }\n    });\n    buildProcess.on('close', (code: number) => {\n      if (xcodebuildOutputFormatter) {\n        xcodebuildOutputFormatter.stdin.end();\n      } else {\n        loader.stop();\n      }\n      if (code !== 0) {\n        printRunDoctorTip();\n        if (!xcodebuildOutputFormatter) {\n          Array.from(prettifyXcodebuildMessages(buildOutput)).forEach((error) =>\n            logger.error(error),\n          );\n        }\n\n        reject(\n          new CLIError(`\n        Failed to build ${platform} project.\n\n        \"xcodebuild\" exited with error code '${code}'. To debug build\n        logs further, consider building your app with Xcode.app, by opening\n        '${xcodeProject.name}'.`),\n        );\n        return;\n      }\n\n      logger.success('Successfully built the app');\n      resolve(buildOutput);\n    });\n  });\n}\n\nfunction xcbeautifyAvailable() {\n  try {\n    child_process.execSync('xcbeautify --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction xcprettyAvailable() {\n  try {\n    child_process.execSync('xcpretty --version', {\n      stdio: [0, 'pipe', 'ignore'],\n    });\n  } catch (error) {\n    return false;\n  }\n  return true;\n}\n\nfunction getProcessOptions<T extends BuildFlags>(\n  args: T,\n): SpawnOptionsWithoutStdio {\n  if (\n    'packager' in args &&\n    typeof args.packager === 'boolean' &&\n    args.packager\n  ) {\n    const terminal =\n      'terminal' in args && typeof args.terminal === 'string'\n        ? args.terminal\n        : '';\n\n    const port =\n      'port' in args && typeof args.port === 'number' ? String(args.port) : '';\n\n    return {\n      env: {\n        ...process.env,\n        RCT_TERMINAL: terminal,\n        RCT_METRO_PORT: port,\n      },\n    };\n  }\n\n  return {\n    env: process.env,\n  };\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAOA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA4E;AAG5E,SAASA,0BAA0B,CAACC,MAAc,EAAe;EAC/D,MAAMC,UAAU,GAAG,wCAAwC;EAC3D,MAAMC,MAAM,GAAG,IAAIC,GAAG,EAAU;EAEhC,IAAIC,KAAK;EACT,OAAO,CAACA,KAAK,GAAGH,UAAU,CAACI,IAAI,CAACL,MAAM,CAAC,MAAM,IAAI,EAAE;IACjD,IAAII,KAAK,CAAC,CAAC,CAAC,EAAE;MACZ;MACAF,MAAM,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,EAAE,CAAC;IAC7B;EACF;EAEA,OAAOL,MAAM;AACf;AAEO,SAASM,YAAY,CAC1BC,YAA4B,EAC5BC,QAAuB,EACvBC,IAAwB,EACxBC,IAAY,EACZC,MAAc,EACdC,IAAgB,EACC;EACjB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,aAAa,GAAGC,gDAAuB,aAAvBA,gDAAuB,uBAAvBA,gDAAuB,CAAGT,QAAQ,CAAC;IAEzD,IAAI,CAACQ,aAAa,EAAE;MAClBD,MAAM,CACJ,KAAIG,oBAAQ,EACT,qBAAoBV,QAAS,yBAAwBW,MAAM,CAACC,MAAM,CACjEC,oCAAkB,CACnB,CAACC,IAAI,CAAC,IAAI,CAAE,GAAE,CAChB,CACF;MACD;IACF;IAEA,MAAMC,cAAc,GAAG,CACrBhB,YAAY,CAACiB,WAAW,GAAG,YAAY,GAAG,UAAU,EACpDjB,YAAY,CAACkB,IAAI,EACjB,IAAIb,IAAI,CAACc,QAAQ,GAAG,CAAC,WAAW,EAAEd,IAAI,CAACc,QAAQ,CAAC,GAAG,EAAE,CAAC,EACtD,IAAId,IAAI,CAACe,WAAW,GAAG,CAAC,kBAAkB,EAAEf,IAAI,CAACe,WAAW,CAAC,GAAG,EAAE,CAAC,EACnE,gBAAgB,EAChBjB,IAAI,EACJ,SAAS,EACTC,MAAM,EACN,cAAc,EACd,CAACF,IAAI,GACA,MAAKA,IAAK,EAAC,GACZC,IAAI,KAAK,OAAO,GACf,oBAAmBM,aAAc,EAAC,GAClC,oBAAmBR,QAAS,EAAC,KAC/BI,IAAI,CAACgB,WAAW,GAAG,GAAG,GAAGhB,IAAI,CAACgB,WAAW,GAAG,EAAE,CAAC,CACnD;IAED,IAAIhB,IAAI,CAACiB,WAAW,EAAE;MACpBN,cAAc,CAACO,IAAI,CAAC,GAAGlB,IAAI,CAACiB,WAAW,CAAC;IAC1C;IAEA,MAAME,MAAM,GAAG,IAAAC,qBAAS,GAAE;IAC1BC,kBAAM,CAACC,IAAI,CACR,YAAWC,gBAAK,CAACC,GAAG,CAClB,sBAAqBb,cAAc,CAACD,IAAI,CAAC,GAAG,CAAE,IAAG,CAClD,EAAC,CACJ;IACD,IAAIe,yBAA6C;IACjD,IAAI,CAACzB,IAAI,CAAC0B,OAAO,EAAE;MACjB,IAAIC,mBAAmB,EAAE,EAAE;QACzBF,yBAAyB,GAAGG,wBAAa,CAACC,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE;UAChEC,KAAK,EAAE,CAAC,MAAM,EAAEC,OAAO,CAACC,MAAM,EAAED,OAAO,CAACE,MAAM;QAChD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIC,iBAAiB,EAAE,EAAE;QAC9BT,yBAAyB,GAAGG,wBAAa,CAACC,KAAK,CAAC,UAAU,EAAE,EAAE,EAAE;UAC9DC,KAAK,EAAE,CAAC,MAAM,EAAEC,OAAO,CAACC,MAAM,EAAED,OAAO,CAACE,MAAM;QAChD,CAAC,CAAC;MACJ;IACF;IAEA,MAAME,YAAY,GAAGP,wBAAa,CAACC,KAAK,CACtC,YAAY,EACZlB,cAAc,EACdyB,iBAAiB,CAACpC,IAAI,CAAC,CACxB;IACD,IAAIqC,WAAW,GAAG,EAAE;IACpBF,YAAY,CAACH,MAAM,CAACM,EAAE,CAAC,MAAM,EAAGC,IAAY,IAAK;MAC/C,MAAMC,UAAU,GAAGD,IAAI,CAACE,QAAQ,EAAE;MAClCJ,WAAW,IAAIG,UAAU;MACzB,IAAIf,yBAAyB,EAAE;QAC7BA,yBAAyB,CAACiB,KAAK,CAACC,KAAK,CAACJ,IAAI,CAAC;MAC7C,CAAC,MAAM;QACL,IAAIlB,kBAAM,CAACuB,SAAS,EAAE,EAAE;UACtBvB,kBAAM,CAACwB,KAAK,CAACL,UAAU,CAAC;QAC1B,CAAC,MAAM;UACLrB,MAAM,CAAC2B,KAAK,CACT,mBAAkB,GAAG,CAACC,MAAM,CAACV,WAAW,CAACW,MAAM,GAAG,EAAE,CAAE,EAAC,CACzD;QACH;MACF;IACF,CAAC,CAAC;IACFb,YAAY,CAACG,EAAE,CAAC,OAAO,EAAGW,IAAY,IAAK;MACzC,IAAIxB,yBAAyB,EAAE;QAC7BA,yBAAyB,CAACiB,KAAK,CAACQ,GAAG,EAAE;MACvC,CAAC,MAAM;QACL/B,MAAM,CAACgC,IAAI,EAAE;MACf;MACA,IAAIF,IAAI,KAAK,CAAC,EAAE;QACd,IAAAG,6BAAiB,GAAE;QACnB,IAAI,CAAC3B,yBAAyB,EAAE;UAC9B4B,KAAK,CAACC,IAAI,CAACrE,0BAA0B,CAACoD,WAAW,CAAC,CAAC,CAACkB,OAAO,CAAEC,KAAK,IAChEnC,kBAAM,CAACmC,KAAK,CAACA,KAAK,CAAC,CACpB;QACH;QAEArD,MAAM,CACJ,KAAIG,oBAAQ,EAAE;AACxB,0BAA0BV,QAAS;AACnC;AACA,+CAA+CqD,IAAK;AACpD;AACA,WAAWtD,YAAY,CAACkB,IAAK,IAAG,CAAC,CACxB;QACD;MACF;MAEAQ,kBAAM,CAACoC,OAAO,CAAC,4BAA4B,CAAC;MAC5CvD,OAAO,CAACmC,WAAW,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASV,mBAAmB,GAAG;EAC7B,IAAI;IACFC,wBAAa,CAAC8B,QAAQ,CAAC,sBAAsB,EAAE;MAC7C5B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO0B,KAAK,EAAE;IACd,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAEA,SAAStB,iBAAiB,GAAG;EAC3B,IAAI;IACFN,wBAAa,CAAC8B,QAAQ,CAAC,oBAAoB,EAAE;MAC3C5B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO0B,KAAK,EAAE;IACd,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAEA,SAASpB,iBAAiB,CACxBpC,IAAO,EACmB;EAC1B,IACE,UAAU,IAAIA,IAAI,IAClB,OAAOA,IAAI,CAAC2D,QAAQ,KAAK,SAAS,IAClC3D,IAAI,CAAC2D,QAAQ,EACb;IACA,MAAMC,QAAQ,GACZ,UAAU,IAAI5D,IAAI,IAAI,OAAOA,IAAI,CAAC4D,QAAQ,KAAK,QAAQ,GACnD5D,IAAI,CAAC4D,QAAQ,GACb,EAAE;IAER,MAAMC,IAAI,GACR,MAAM,IAAI7D,IAAI,IAAI,OAAOA,IAAI,CAAC6D,IAAI,KAAK,QAAQ,GAAGC,MAAM,CAAC9D,IAAI,CAAC6D,IAAI,CAAC,GAAG,EAAE;IAE1E,OAAO;MACLE,GAAG,EAAE;QACH,GAAGhC,OAAO,CAACgC,GAAG;QACdC,YAAY,EAAEJ,QAAQ;QACtBK,cAAc,EAAEJ;MAClB;IACF,CAAC;EACH;EAEA,OAAO;IACLE,GAAG,EAAEhC,OAAO,CAACgC;EACf,CAAC;AACH"}