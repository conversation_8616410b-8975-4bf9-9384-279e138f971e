{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["export {\n  getDependencyConfig,\n  getProjectConfig,\n  findPodfilePaths,\n  installPods,\n  findXcodeProject,\n  findPbxprojFile,\n} from '@react-native-community/cli-config-apple';\n\nexport {getBuildOptions} from './commands/buildCommand/buildOptions';\nexport {getLogOptions} from './commands/logCommand/logOptions';\nexport {getRunOptions} from './commands/runCommand/runOptions';\n\nexport {default as createBuild} from './commands/buildCommand/createBuild';\nexport {default as createLog} from './commands/logCommand/createLog';\nexport {default as createRun} from './commands/runCommand/createRun';\n\nexport {default as getArchitecture} from './tools/getArchitecture';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AASA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAmE"}