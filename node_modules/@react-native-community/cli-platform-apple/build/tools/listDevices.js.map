{"version": 3, "names": ["parseXcdeviceList", "text", "sdkNames", "rawOutput", "JSON", "parse", "devices", "filter", "device", "includes", "stripPlatform", "platform", "sort", "simulator", "map", "isAvailable", "available", "name", "udid", "identifier", "sdk", "version", "operatingSystemVersion", "availabilityError", "error", "description", "type", "listDevices", "xcdeviceOutput", "execa", "sync", "stdout", "parsedXcdeviceOutput", "simctlOutput", "parsedSimctlOutput", "Object", "keys", "key", "reduce", "acc", "val", "concat", "merged", "matchedUdids", "Set", "for<PERSON>ach", "first", "match", "find", "second", "add", "push", "item", "has", "replace"], "sources": ["../../src/tools/listDevices.ts"], "sourcesContent": ["import {Device} from '../types';\nimport execa from 'execa';\n\ntype DeviceOutput = {\n  modelCode: string;\n  simulator: boolean;\n  modelName: string;\n  error: {\n    code: number;\n    failureReason: string;\n    underlyingErrors: [\n      {\n        code: number;\n        failureReason: string;\n        description: string;\n        recoverySuggestion: string;\n        domain: string;\n      },\n    ];\n    description: string;\n    recoverySuggestion: string;\n    domain: string;\n  };\n  operatingSystemVersion: string;\n  identifier: string;\n  platform: string;\n  architecture: string;\n  interface: string;\n  available: boolean;\n  name: string;\n  modelUTI: string;\n};\n\nconst parseXcdeviceList = (text: string, sdkNames: string[] = []): Device[] => {\n  const rawOutput = JSON.parse(text) as DeviceOutput[];\n\n  const devices: Device[] = rawOutput\n    .filter((device) => sdkNames.includes(stripPlatform(device?.platform)))\n    .sort((device) => (device.simulator ? 1 : -1))\n    .map((device) => ({\n      isAvailable: device.available,\n      name: device.name,\n      udid: device.identifier,\n      sdk: device.platform,\n      version: device.operatingSystemVersion,\n      availabilityError: device.error?.description,\n      type: device.simulator ? 'simulator' : 'device',\n    }));\n  return devices;\n};\n\n/**\n * Executes `xcrun xcdevice list` and `xcrun simctl list --json devices`, and connects parsed output of these two commands. We are running these two commands as they are necessary to display both physical devices and simulators. However, it's important to note that neither command provides a combined output of both.\n * @param sdkNames\n * @returns List of available devices and simulators.\n */\nasync function listDevices(sdkNames: string[]): Promise<Device[]> {\n  const xcdeviceOutput = execa.sync('xcrun', ['xcdevice', 'list']).stdout;\n  const parsedXcdeviceOutput = parseXcdeviceList(xcdeviceOutput, sdkNames);\n\n  const simctlOutput = JSON.parse(\n    execa.sync('xcrun', ['simctl', 'list', '--json', 'devices']).stdout,\n  );\n\n  const parsedSimctlOutput: Device[] = Object.keys(simctlOutput.devices)\n    .map((key) => simctlOutput.devices[key])\n    .reduce((acc, val) => acc.concat(val), []);\n\n  const merged: Device[] = [];\n  const matchedUdids = new Set();\n\n  parsedXcdeviceOutput.forEach((first) => {\n    const match = parsedSimctlOutput.find(\n      (second) => first.udid === second.udid,\n    );\n    if (match) {\n      matchedUdids.add(first.udid);\n      merged.push({...first, ...match});\n    } else {\n      merged.push({...first});\n    }\n  });\n\n  parsedSimctlOutput.forEach((item) => {\n    if (!matchedUdids.has(item.udid)) {\n      merged.push({...item});\n    }\n  });\n\n  return merged.filter(({isAvailable}) => isAvailable === true);\n}\n\nexport function stripPlatform(platform: string): string {\n  return platform.replace('com.apple.platform.', '');\n}\n\nexport default listDevices;\n"], "mappings": ";;;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAgC1B,MAAMA,iBAAiB,GAAG,CAACC,IAAY,EAAEC,QAAkB,GAAG,EAAE,KAAe;EAC7E,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAmB;EAEpD,MAAMK,OAAiB,GAAGH,SAAS,CAChCI,MAAM,CAAEC,MAAM,IAAKN,QAAQ,CAACO,QAAQ,CAACC,aAAa,CAACF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,QAAQ,CAAC,CAAC,CAAC,CACtEC,IAAI,CAAEJ,MAAM,IAAMA,MAAM,CAACK,SAAS,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC,CAC7CC,GAAG,CAAEN,MAAM;IAAA;IAAA,OAAM;MAChBO,WAAW,EAAEP,MAAM,CAACQ,SAAS;MAC7BC,IAAI,EAAET,MAAM,CAACS,IAAI;MACjBC,IAAI,EAAEV,MAAM,CAACW,UAAU;MACvBC,GAAG,EAAEZ,MAAM,CAACG,QAAQ;MACpBU,OAAO,EAAEb,MAAM,CAACc,sBAAsB;MACtCC,iBAAiB,mBAAEf,MAAM,CAACgB,KAAK,kDAAZ,cAAcC,WAAW;MAC5CC,IAAI,EAAElB,MAAM,CAACK,SAAS,GAAG,WAAW,GAAG;IACzC,CAAC;EAAA,CAAC,CAAC;EACL,OAAOP,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,eAAeqB,WAAW,CAACzB,QAAkB,EAAqB;EAChE,MAAM0B,cAAc,GAAGC,gBAAK,CAACC,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAACC,MAAM;EACvE,MAAMC,oBAAoB,GAAGhC,iBAAiB,CAAC4B,cAAc,EAAE1B,QAAQ,CAAC;EAExE,MAAM+B,YAAY,GAAG7B,IAAI,CAACC,KAAK,CAC7BwB,gBAAK,CAACC,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAACC,MAAM,CACpE;EAED,MAAMG,kBAA4B,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC3B,OAAO,CAAC,CACnEQ,GAAG,CAAEuB,GAAG,IAAKJ,YAAY,CAAC3B,OAAO,CAAC+B,GAAG,CAAC,CAAC,CACvCC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,CAACE,MAAM,CAACD,GAAG,CAAC,EAAE,EAAE,CAAC;EAE5C,MAAME,MAAgB,GAAG,EAAE;EAC3B,MAAMC,YAAY,GAAG,IAAIC,GAAG,EAAE;EAE9BZ,oBAAoB,CAACa,OAAO,CAAEC,KAAK,IAAK;IACtC,MAAMC,KAAK,GAAGb,kBAAkB,CAACc,IAAI,CAClCC,MAAM,IAAKH,KAAK,CAAC5B,IAAI,KAAK+B,MAAM,CAAC/B,IAAI,CACvC;IACD,IAAI6B,KAAK,EAAE;MACTJ,YAAY,CAACO,GAAG,CAACJ,KAAK,CAAC5B,IAAI,CAAC;MAC5BwB,MAAM,CAACS,IAAI,CAAC;QAAC,GAAGL,KAAK;QAAE,GAAGC;MAAK,CAAC,CAAC;IACnC,CAAC,MAAM;MACLL,MAAM,CAACS,IAAI,CAAC;QAAC,GAAGL;MAAK,CAAC,CAAC;IACzB;EACF,CAAC,CAAC;EAEFZ,kBAAkB,CAACW,OAAO,CAAEO,IAAI,IAAK;IACnC,IAAI,CAACT,YAAY,CAACU,GAAG,CAACD,IAAI,CAAClC,IAAI,CAAC,EAAE;MAChCwB,MAAM,CAACS,IAAI,CAAC;QAAC,GAAGC;MAAI,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EAEF,OAAOV,MAAM,CAACnC,MAAM,CAAC,CAAC;IAACQ;EAAW,CAAC,KAAKA,WAAW,KAAK,IAAI,CAAC;AAC/D;AAEO,SAASL,aAAa,CAACC,QAAgB,EAAU;EACtD,OAAOA,QAAQ,CAAC2C,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;AACpD;AAAC,eAEc3B,WAAW;AAAA"}