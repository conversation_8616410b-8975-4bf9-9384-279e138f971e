{"version": 3, "names": ["isErrorLike", "err", "Boolean", "message", "parseTargetList", "json", "info", "JSON", "parse", "project", "workspace", "undefined", "error", "match", "Error", "getInfo", "projectInfo", "sourceDir", "isWorkspace", "xcodebuild", "execa", "sync", "stdout", "xmlParser", "XMLParser", "ignoreAttributes", "xcworkspacedata", "path", "join", "name", "fs", "readFileSync", "encoding", "fileRef", "Workspace", "FileRef", "refs", "Array", "isArray", "reduce", "result", "ref", "location", "endsWith", "replace", "schemes", "length", "concat"], "sources": ["../../src/tools/getInfo.ts"], "sourcesContent": ["import type {IOSProjectInfo} from '@react-native-community/cli-types';\nimport execa from 'execa';\nimport {XMLParser} from 'fast-xml-parser';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport type {IosInfo} from '../types';\n\nfunction isErrorLike(err: unknown): err is {message: string} {\n  return Boolean(\n    err &&\n      typeof err === 'object' &&\n      'message' in err &&\n      typeof err.message === 'string',\n  );\n}\n\nfunction parseTargetList(json: string): IosInfo | undefined {\n  try {\n    const info = JSON.parse(json);\n\n    if ('project' in info) {\n      return info.project;\n    } else if ('workspace' in info) {\n      return info.workspace;\n    }\n\n    return undefined;\n  } catch (error) {\n    if (isErrorLike(error)) {\n      const match = error.message.match(/xcodebuild: error: (.*)/);\n      if (match) {\n        throw new Error(match[0]);\n      }\n    }\n\n    throw error;\n  }\n}\n\nexport function getInfo(\n  projectInfo: IOSProjectInfo,\n  sourceDir: string,\n): IosInfo | undefined {\n  if (!projectInfo.isWorkspace) {\n    const xcodebuild = execa.sync('xcodebuild', ['-list', '-json']);\n    return parseTargetList(xcodebuild.stdout);\n  }\n\n  const xmlParser = new XMLParser({ignoreAttributes: false});\n  const xcworkspacedata = path.join(\n    sourceDir,\n    projectInfo.name,\n    'contents.xcworkspacedata',\n  );\n  const workspace = fs.readFileSync(xcworkspacedata, {encoding: 'utf-8'});\n  const fileRef = xmlParser.parse(workspace).Workspace.FileRef;\n  const refs = Array.isArray(fileRef) ? fileRef : [fileRef];\n\n  return refs.reduce<IosInfo | undefined>((result, ref) => {\n    const location = ref['@_location'];\n\n    if (!location.endsWith('.xcodeproj')) {\n      return result;\n    }\n\n    // Ignore the project generated by CocoaPods\n    if (location.endsWith('/Pods.xcodeproj')) {\n      return result;\n    }\n\n    const xcodebuild = execa.sync('xcodebuild', [\n      '-list',\n      '-json',\n      '-project',\n      path.join(sourceDir, location.replace('group:', '')),\n    ]);\n    const info = parseTargetList(xcodebuild.stdout);\n    if (!info) {\n      return result;\n    }\n\n    const schemes = info.schemes;\n\n    // If this is the first project, use it as the \"main\" project\n    if (!result) {\n      if (!Array.isArray(schemes)) {\n        info.schemes = [];\n      }\n      return info;\n    }\n\n    if (!Array.isArray(result.schemes)) {\n      throw new Error(\"This shouldn't happen since we set it earlier\");\n    }\n\n    // For subsequent projects, merge schemes list\n    if (Array.isArray(schemes) && schemes.length > 0) {\n      result.schemes = result.schemes.concat(schemes);\n    }\n\n    return result;\n  }, undefined);\n}\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA6B;AAAA;AAAA;AAG7B,SAASA,WAAW,CAACC,GAAY,EAA4B;EAC3D,OAAOC,OAAO,CACZD,GAAG,IACD,OAAOA,GAAG,KAAK,QAAQ,IACvB,SAAS,IAAIA,GAAG,IAChB,OAAOA,GAAG,CAACE,OAAO,KAAK,QAAQ,CAClC;AACH;AAEA,SAASC,eAAe,CAACC,IAAY,EAAuB;EAC1D,IAAI;IACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IAE7B,IAAI,SAAS,IAAIC,IAAI,EAAE;MACrB,OAAOA,IAAI,CAACG,OAAO;IACrB,CAAC,MAAM,IAAI,WAAW,IAAIH,IAAI,EAAE;MAC9B,OAAOA,IAAI,CAACI,SAAS;IACvB;IAEA,OAAOC,SAAS;EAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIZ,WAAW,CAACY,KAAK,CAAC,EAAE;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAACT,OAAO,CAACU,KAAK,CAAC,yBAAyB,CAAC;MAC5D,IAAIA,KAAK,EAAE;QACT,MAAM,IAAIC,KAAK,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF;IAEA,MAAMD,KAAK;EACb;AACF;AAEO,SAASG,OAAO,CACrBC,WAA2B,EAC3BC,SAAiB,EACI;EACrB,IAAI,CAACD,WAAW,CAACE,WAAW,EAAE;IAC5B,MAAMC,UAAU,GAAGC,gBAAK,CAACC,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,OAAOjB,eAAe,CAACe,UAAU,CAACG,MAAM,CAAC;EAC3C;EAEA,MAAMC,SAAS,GAAG,KAAIC,0BAAS,EAAC;IAACC,gBAAgB,EAAE;EAAK,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGC,IAAI,GAACC,IAAI,CAC/BX,SAAS,EACTD,WAAW,CAACa,IAAI,EAChB,0BAA0B,CAC3B;EACD,MAAMnB,SAAS,GAAGoB,EAAE,GAACC,YAAY,CAACL,eAAe,EAAE;IAACM,QAAQ,EAAE;EAAO,CAAC,CAAC;EACvE,MAAMC,OAAO,GAAGV,SAAS,CAACf,KAAK,CAACE,SAAS,CAAC,CAACwB,SAAS,CAACC,OAAO;EAC5D,MAAMC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAEzD,OAAOG,IAAI,CAACG,MAAM,CAAsB,CAACC,MAAM,EAAEC,GAAG,KAAK;IACvD,MAAMC,QAAQ,GAAGD,GAAG,CAAC,YAAY,CAAC;IAElC,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;MACpC,OAAOH,MAAM;IACf;;IAEA;IACA,IAAIE,QAAQ,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MACxC,OAAOH,MAAM;IACf;IAEA,MAAMrB,UAAU,GAAGC,gBAAK,CAACC,IAAI,CAAC,YAAY,EAAE,CAC1C,OAAO,EACP,OAAO,EACP,UAAU,EACVM,IAAI,GAACC,IAAI,CAACX,SAAS,EAAEyB,QAAQ,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CACrD,CAAC;IACF,MAAMtC,IAAI,GAAGF,eAAe,CAACe,UAAU,CAACG,MAAM,CAAC;IAC/C,IAAI,CAAChB,IAAI,EAAE;MACT,OAAOkC,MAAM;IACf;IAEA,MAAMK,OAAO,GAAGvC,IAAI,CAACuC,OAAO;;IAE5B;IACA,IAAI,CAACL,MAAM,EAAE;MACX,IAAI,CAACH,KAAK,CAACC,OAAO,CAACO,OAAO,CAAC,EAAE;QAC3BvC,IAAI,CAACuC,OAAO,GAAG,EAAE;MACnB;MACA,OAAOvC,IAAI;IACb;IAEA,IAAI,CAAC+B,KAAK,CAACC,OAAO,CAACE,MAAM,CAACK,OAAO,CAAC,EAAE;MAClC,MAAM,IAAI/B,KAAK,CAAC,+CAA+C,CAAC;IAClE;;IAEA;IACA,IAAIuB,KAAK,CAACC,OAAO,CAACO,OAAO,CAAC,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAChDN,MAAM,CAACK,OAAO,GAAGL,MAAM,CAACK,OAAO,CAACE,MAAM,CAACF,OAAO,CAAC;IACjD;IAEA,OAAOL,MAAM;EACf,CAAC,EAAE7B,SAAS,CAAC;AACf"}