{"version": 3, "names": ["findMatchingSimulator", "simulators", "findOptions", "devices", "simulatorVersion", "simulatorName", "simulator", "parsedSimulatorName", "match", "undefined", "fallbackMatch", "sortedDevices", "Object", "fromEntries", "entries", "sort", "a", "b", "Number", "includes", "versionDescriptor", "device", "version", "test", "replace", "endsWith", "i", "availability", "isAvailable", "lastBootedAt", "simulatorDescriptor", "udid", "name", "state"], "sources": ["../../src/tools/findMatchingSimulator.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {Device} from '../types';\n\n/**\n * Takes in a parsed simulator list and a desired name, and returns an object with the matching simulator. The desired\n * name can optionally include the iOS version in between parenthesis after the device name. Ex: \"iPhone 6 (9.2)\" in\n * which case it'll attempt to find a simulator with the exact version specified.\n *\n * If the simulatorString argument is null, we'll go into default mode and return the currently booted simulator,\n * the last booted simulator or\n * if none is booted, it will be the first in the list.\n *\n * @param simulators a parsed list from `xcrun simctl list --json devices` command\n * @param findOptions null or an object containing:\n * ```\n * {\n *    simulator: name of desired simulator\n *    udid: udid of desired simulator\n * }\n * ```\n * If null, it will use the currently booted simulator, or if none are booted, the first in the list.\n */\nfunction findMatchingSimulator(\n  simulators: {devices: {[index: string]: Array<Device>}},\n  findOptions?: null | {simulator?: string; udid?: string},\n) {\n  if (!simulators.devices) {\n    return null;\n  }\n  const devices = simulators.devices;\n  let simulatorVersion;\n  let simulatorName = null;\n\n  if (findOptions && findOptions.simulator) {\n    const parsedSimulatorName = findOptions.simulator.match(\n      /(.*)? (?:\\((\\d+\\.\\d+)?\\))$/,\n    );\n    if (parsedSimulatorName && parsedSimulatorName[2] !== undefined) {\n      simulatorVersion = parsedSimulatorName[2];\n      simulatorName = parsedSimulatorName[1];\n    } else {\n      simulatorName = findOptions.simulator;\n    }\n  }\n\n  let match;\n  let fallbackMatch;\n\n  const sortedDevices = Object.fromEntries(\n    Object.entries(devices).sort(\n      (a, b) => Number(b[0].includes('iOS')) - Number(a[0].includes('iOS')),\n    ),\n  );\n\n  for (const versionDescriptor in sortedDevices) {\n    const device = sortedDevices[versionDescriptor];\n    let version = versionDescriptor;\n\n    if (/^com\\.apple\\.CoreSimulator\\.SimRuntime\\./g.test(version)) {\n      // Transform \"com.apple.CoreSimulator.SimRuntime.iOS-12-2\" into \"iOS 12.2\"\n      version = version.replace(\n        /^com\\.apple\\.CoreSimulator\\.SimRuntime\\.([^-]+)-([^-]+)-([^-]+)$/g,\n        '$1 $2.$3',\n      );\n    }\n\n    // Making sure the version of the simulator is an iOS or tvOS (Removes Apple Watch, etc)\n    if (!version.includes('iOS') && !version.includes('tvOS')) {\n      continue;\n    }\n    if (simulatorVersion && !version.endsWith(simulatorVersion)) {\n      continue;\n    }\n    for (const i in device) {\n      const simulator = device[i];\n      // Skipping non-available simulator\n      if (\n        simulator.availability !== '(available)' &&\n        // @ts-ignore verify isAvailable parameter\n        simulator.isAvailable !== 'YES' &&\n        simulator.isAvailable !== true\n      ) {\n        continue;\n      }\n      const lastBootedAt = simulator.lastBootedAt;\n      const simulatorDescriptor: Device = {\n        udid: simulator.udid,\n        name: simulator.name,\n        state: simulator.state,\n        version,\n      };\n      if (findOptions && findOptions.udid) {\n        if (simulator.udid === findOptions.udid) {\n          return simulatorDescriptor;\n        }\n      } else {\n        if (simulator.state === 'Booted' && simulatorName === null) {\n          return simulatorDescriptor;\n        }\n        if (simulator.name === simulatorName && !match) {\n          match = simulatorDescriptor;\n        }\n        // If no match found, use first available simulator that was booted before\n        if (!!lastBootedAt && !match) {\n          fallbackMatch = simulatorDescriptor;\n        }\n        // Keeps track of the first available simulator for use if we can't find one above.\n        if (simulatorName === null && !match) {\n          match = simulatorDescriptor;\n        }\n      }\n    }\n  }\n\n  return match ?? fallbackMatch ?? null;\n}\n\nexport default findMatchingSimulator;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,CAC5BC,UAAuD,EACvDC,WAAwD,EACxD;EACA,IAAI,CAACD,UAAU,CAACE,OAAO,EAAE;IACvB,OAAO,IAAI;EACb;EACA,MAAMA,OAAO,GAAGF,UAAU,CAACE,OAAO;EAClC,IAAIC,gBAAgB;EACpB,IAAIC,aAAa,GAAG,IAAI;EAExB,IAAIH,WAAW,IAAIA,WAAW,CAACI,SAAS,EAAE;IACxC,MAAMC,mBAAmB,GAAGL,WAAW,CAACI,SAAS,CAACE,KAAK,CACrD,4BAA4B,CAC7B;IACD,IAAID,mBAAmB,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAKE,SAAS,EAAE;MAC/DL,gBAAgB,GAAGG,mBAAmB,CAAC,CAAC,CAAC;MACzCF,aAAa,GAAGE,mBAAmB,CAAC,CAAC,CAAC;IACxC,CAAC,MAAM;MACLF,aAAa,GAAGH,WAAW,CAACI,SAAS;IACvC;EACF;EAEA,IAAIE,KAAK;EACT,IAAIE,aAAa;EAEjB,MAAMC,aAAa,GAAGC,MAAM,CAACC,WAAW,CACtCD,MAAM,CAACE,OAAO,CAACX,OAAO,CAAC,CAACY,IAAI,CAC1B,CAACC,CAAC,EAAEC,CAAC,KAAKC,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAGD,MAAM,CAACF,CAAC,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,KAAK,CAAC,CAAC,CACtE,CACF;EAED,KAAK,MAAMC,iBAAiB,IAAIT,aAAa,EAAE;IAC7C,MAAMU,MAAM,GAAGV,aAAa,CAACS,iBAAiB,CAAC;IAC/C,IAAIE,OAAO,GAAGF,iBAAiB;IAE/B,IAAI,2CAA2C,CAACG,IAAI,CAACD,OAAO,CAAC,EAAE;MAC7D;MACAA,OAAO,GAAGA,OAAO,CAACE,OAAO,CACvB,mEAAmE,EACnE,UAAU,CACX;IACH;;IAEA;IACA,IAAI,CAACF,OAAO,CAACH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACG,OAAO,CAACH,QAAQ,CAAC,MAAM,CAAC,EAAE;MACzD;IACF;IACA,IAAIf,gBAAgB,IAAI,CAACkB,OAAO,CAACG,QAAQ,CAACrB,gBAAgB,CAAC,EAAE;MAC3D;IACF;IACA,KAAK,MAAMsB,CAAC,IAAIL,MAAM,EAAE;MACtB,MAAMf,SAAS,GAAGe,MAAM,CAACK,CAAC,CAAC;MAC3B;MACA,IACEpB,SAAS,CAACqB,YAAY,KAAK,aAAa;MACxC;MACArB,SAAS,CAACsB,WAAW,KAAK,KAAK,IAC/BtB,SAAS,CAACsB,WAAW,KAAK,IAAI,EAC9B;QACA;MACF;MACA,MAAMC,YAAY,GAAGvB,SAAS,CAACuB,YAAY;MAC3C,MAAMC,mBAA2B,GAAG;QAClCC,IAAI,EAAEzB,SAAS,CAACyB,IAAI;QACpBC,IAAI,EAAE1B,SAAS,CAAC0B,IAAI;QACpBC,KAAK,EAAE3B,SAAS,CAAC2B,KAAK;QACtBX;MACF,CAAC;MACD,IAAIpB,WAAW,IAAIA,WAAW,CAAC6B,IAAI,EAAE;QACnC,IAAIzB,SAAS,CAACyB,IAAI,KAAK7B,WAAW,CAAC6B,IAAI,EAAE;UACvC,OAAOD,mBAAmB;QAC5B;MACF,CAAC,MAAM;QACL,IAAIxB,SAAS,CAAC2B,KAAK,KAAK,QAAQ,IAAI5B,aAAa,KAAK,IAAI,EAAE;UAC1D,OAAOyB,mBAAmB;QAC5B;QACA,IAAIxB,SAAS,CAAC0B,IAAI,KAAK3B,aAAa,IAAI,CAACG,KAAK,EAAE;UAC9CA,KAAK,GAAGsB,mBAAmB;QAC7B;QACA;QACA,IAAI,CAAC,CAACD,YAAY,IAAI,CAACrB,KAAK,EAAE;UAC5BE,aAAa,GAAGoB,mBAAmB;QACrC;QACA;QACA,IAAIzB,aAAa,KAAK,IAAI,IAAI,CAACG,KAAK,EAAE;UACpCA,KAAK,GAAGsB,mBAAmB;QAC7B;MACF;IACF;EACF;EAEA,OAAOtB,KAAK,IAAIE,aAAa,IAAI,IAAI;AACvC;AAAC,eAEcV,qBAAqB;AAAA"}