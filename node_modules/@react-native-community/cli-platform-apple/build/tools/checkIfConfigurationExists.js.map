{"version": 3, "names": ["checkIfConfigurationExists", "configurations", "mode", "length", "logger", "warn", "includes", "CLIError", "join"], "sources": ["../../src/tools/checkIfConfigurationExists.ts"], "sourcesContent": ["import {CLIError, logger} from '@react-native-community/cli-tools';\n\nexport function checkIfConfigurationExists(\n  configurations: string[],\n  mode: string,\n) {\n  if (configurations.length === 0) {\n    logger.warn(`Unable to check whether \"${mode}\" exists in your project`);\n    return;\n  }\n\n  if (!configurations.includes(mode)) {\n    throw new CLIError(\n      `Configuration \"${mode}\" does not exist in your project. Please use one of the existing configurations: ${configurations.join(\n        ', ',\n      )}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEO,SAASA,0BAA0B,CACxCC,cAAwB,EACxBC,IAAY,EACZ;EACA,IAAID,cAAc,CAACE,MAAM,KAAK,CAAC,EAAE;IAC/BC,kBAAM,CAACC,IAAI,CAAE,4BAA2BH,IAAK,0BAAyB,CAAC;IACvE;EACF;EAEA,IAAI,CAACD,cAAc,CAACK,QAAQ,CAACJ,IAAI,CAAC,EAAE;IAClC,MAAM,KAAIK,oBAAQ,EACf,kBAAiBL,IAAK,oFAAmFD,cAAc,CAACO,IAAI,CAC3H,IAAI,CACJ,EAAC,CACJ;EACH;AACF"}