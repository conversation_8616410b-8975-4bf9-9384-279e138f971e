{"version": 3, "names": ["getVersionFromDevice", "version", "match", "promptForSchemeSelection", "schemes", "scheme", "prompt", "name", "type", "message", "choices", "map", "value", "title", "promptForConfigurationSelection", "configurations", "configuration", "promptForDeviceSelection", "devices", "device", "filter", "d", "availability", "isAvailable", "availabilityError", "chalk", "red", "bold", "disabled", "min", "promptForDeviceToTailLogs", "platformReadableName", "simulators", "udid", "simulator", "trim"], "sources": ["../../src/tools/prompts.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport {Device} from '../types';\nimport {prompt} from '@react-native-community/cli-tools';\n\nfunction getVersionFromDevice({version}: Device) {\n  return version ? ` (${version.match(/^(\\d+\\.\\d+)/)?.[1]})` : '';\n}\n\nexport async function promptForSchemeSelection(\n  schemes: string[],\n): Promise<string> {\n  const {scheme} = await prompt({\n    name: 'scheme',\n    type: 'select',\n    message: 'Select the scheme you want to use',\n    choices: schemes.map((value) => ({\n      title: value,\n      value: value,\n    })),\n  });\n\n  return scheme;\n}\n\nexport async function promptForConfigurationSelection(\n  configurations: string[],\n): Promise<string> {\n  const {configuration} = await prompt({\n    name: 'configuration',\n    type: 'select',\n    message: 'Select the configuration you want to use',\n    choices: configurations.map((value) => ({\n      title: value,\n      value: value,\n    })),\n  });\n\n  return configuration;\n}\n\nexport async function promptForDeviceSelection(\n  devices: Device[],\n): Promise<Device | undefined> {\n  const {device} = await prompt({\n    type: 'select',\n    name: 'device',\n    message: 'Select the device you want to use',\n    choices: devices\n      .filter(({type}) => type === 'device' || type === 'simulator')\n      .map((d) => {\n        const availability =\n          !d.isAvailable && !!d.availabilityError\n            ? chalk.red(`(unavailable - ${d.availabilityError})`)\n            : '';\n\n        return {\n          title: `${chalk.bold(\n            `${d.name}${getVersionFromDevice(d)}`,\n          )} ${availability}`,\n          value: d,\n          disabled: !d.isAvailable,\n        };\n      }),\n    min: 1,\n  });\n  return device;\n}\n\nexport async function promptForDeviceToTailLogs(\n  platformReadableName: string,\n  simulators: Device[],\n): Promise<string> {\n  const {udid} = await prompt({\n    type: 'select',\n    name: 'udid',\n    message: `Select ${platformReadableName} simulators to tail logs from`,\n    choices: simulators.map((simulator) => ({\n      title: `${simulator.name}${getVersionFromDevice(simulator)}`.trim(),\n      value: simulator.udid,\n    })),\n  });\n\n  return udid;\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAEzD,SAASA,oBAAoB,CAAC;EAACC;AAAe,CAAC,EAAE;EAAA;EAC/C,OAAOA,OAAO,GAAI,KAAE,kBAAEA,OAAO,CAACC,KAAK,CAAC,aAAa,CAAC,mDAA5B,eAA+B,CAAC,CAAE,GAAE,GAAG,EAAE;AACjE;AAEO,eAAeC,wBAAwB,CAC5CC,OAAiB,EACA;EACjB,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAC,kBAAM,EAAC;IAC5BC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAEN,OAAO,CAACO,GAAG,CAAEC,KAAK,KAAM;MAC/BC,KAAK,EAAED,KAAK;MACZA,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOP,MAAM;AACf;AAEO,eAAeS,+BAA+B,CACnDC,cAAwB,EACP;EACjB,MAAM;IAACC;EAAa,CAAC,GAAG,MAAM,IAAAV,kBAAM,EAAC;IACnCC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,0CAA0C;IACnDC,OAAO,EAAEK,cAAc,CAACJ,GAAG,CAAEC,KAAK,KAAM;MACtCC,KAAK,EAAED,KAAK;MACZA,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOI,aAAa;AACtB;AAEO,eAAeC,wBAAwB,CAC5CC,OAAiB,EACY;EAC7B,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAb,kBAAM,EAAC;IAC5BE,IAAI,EAAE,QAAQ;IACdD,IAAI,EAAE,QAAQ;IACdE,OAAO,EAAE,mCAAmC;IAC5CC,OAAO,EAAEQ,OAAO,CACbE,MAAM,CAAC,CAAC;MAACZ;IAAI,CAAC,KAAKA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,WAAW,CAAC,CAC7DG,GAAG,CAAEU,CAAC,IAAK;MACV,MAAMC,YAAY,GAChB,CAACD,CAAC,CAACE,WAAW,IAAI,CAAC,CAACF,CAAC,CAACG,iBAAiB,GACnCC,gBAAK,CAACC,GAAG,CAAE,kBAAiBL,CAAC,CAACG,iBAAkB,GAAE,CAAC,GACnD,EAAE;MAER,OAAO;QACLX,KAAK,EAAG,GAAEY,gBAAK,CAACE,IAAI,CACjB,GAAEN,CAAC,CAACd,IAAK,GAAEP,oBAAoB,CAACqB,CAAC,CAAE,EAAC,CACrC,IAAGC,YAAa,EAAC;QACnBV,KAAK,EAAES,CAAC;QACRO,QAAQ,EAAE,CAACP,CAAC,CAACE;MACf,CAAC;IACH,CAAC,CAAC;IACJM,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOV,MAAM;AACf;AAEO,eAAeW,yBAAyB,CAC7CC,oBAA4B,EAC5BC,UAAoB,EACH;EACjB,MAAM;IAACC;EAAI,CAAC,GAAG,MAAM,IAAA3B,kBAAM,EAAC;IAC1BE,IAAI,EAAE,QAAQ;IACdD,IAAI,EAAE,MAAM;IACZE,OAAO,EAAG,UAASsB,oBAAqB,+BAA8B;IACtErB,OAAO,EAAEsB,UAAU,CAACrB,GAAG,CAAEuB,SAAS,KAAM;MACtCrB,KAAK,EAAG,GAAEqB,SAAS,CAAC3B,IAAK,GAAEP,oBAAoB,CAACkC,SAAS,CAAE,EAAC,CAACC,IAAI,EAAE;MACnEvB,KAAK,EAAEsB,SAAS,CAACD;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOA,IAAI;AACb"}