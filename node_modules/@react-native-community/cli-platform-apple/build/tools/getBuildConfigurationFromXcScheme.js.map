{"version": 3, "names": ["xmlParser", "XMLParser", "ignoreAttributes", "getBuildConfigurationFromXcScheme", "scheme", "configuration", "sourceDir", "projectInfo", "xcProject", "fs", "readdirSync", "find", "dir", "endsWith", "xmlScheme", "readFileSync", "path", "join", "encoding", "Scheme", "parse", "LaunchAction", "projectSchemes", "schemes", "length", "map", "name", "chalk", "bold", "CLIError"], "sources": ["../../src/tools/getBuildConfigurationFromXcScheme.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport {XMLParser} from 'fast-xml-parser';\nimport fs from 'fs';\nimport path from 'path';\nimport {IosInfo} from '../types';\n\nconst xmlParser = new XMLParser({ignoreAttributes: false});\n\nexport function getBuildConfigurationFromXcScheme(\n  scheme: string,\n  configuration: string,\n  sourceDir: string,\n  projectInfo: IosInfo | undefined,\n): string {\n  // can not assume .xcodeproj exists.\n  // for more info see: https://github.com/react-native-community/cli/pull/2196\n  try {\n    const xcProject = fs\n      .readdirSync(sourceDir)\n      .find((dir) => dir.endsWith('.xcodeproj'));\n\n    if (xcProject) {\n      const xmlScheme = fs.readFileSync(\n        path.join(\n          sourceDir,\n          xcProject,\n          'xcshareddata',\n          'xcschemes',\n          `${scheme}.xcscheme`,\n        ),\n        {\n          encoding: 'utf-8',\n        },\n      );\n\n      const {Scheme} = xmlParser.parse(xmlScheme);\n\n      return Scheme.LaunchAction['@_buildConfiguration'];\n    }\n  } catch {\n    const projectSchemes =\n      projectInfo?.schemes && projectInfo.schemes.length > 0\n        ? `${projectInfo.schemes.map((name) => chalk.bold(name)).join(', ')}`\n        : 'No schemes';\n\n    throw new CLIError(\n      `Could not load the shared scheme for ${scheme}. Your project configuration includes: ${projectSchemes}. Please ensure a valid .xcscheme file exists in xcshareddata/xcschemes.`,\n    );\n  }\n\n  return configuration;\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAGxB,MAAMA,SAAS,GAAG,KAAIC,0BAAS,EAAC;EAACC,gBAAgB,EAAE;AAAK,CAAC,CAAC;AAEnD,SAASC,iCAAiC,CAC/CC,MAAc,EACdC,aAAqB,EACrBC,SAAiB,EACjBC,WAAgC,EACxB;EACR;EACA;EACA,IAAI;IACF,MAAMC,SAAS,GAAGC,aAAE,CACjBC,WAAW,CAACJ,SAAS,CAAC,CACtBK,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE5C,IAAIL,SAAS,EAAE;MACb,MAAMM,SAAS,GAAGL,aAAE,CAACM,YAAY,CAC/BC,eAAI,CAACC,IAAI,CACPX,SAAS,EACTE,SAAS,EACT,cAAc,EACd,WAAW,EACV,GAAEJ,MAAO,WAAU,CACrB,EACD;QACEc,QAAQ,EAAE;MACZ,CAAC,CACF;MAED,MAAM;QAACC;MAAM,CAAC,GAAGnB,SAAS,CAACoB,KAAK,CAACN,SAAS,CAAC;MAE3C,OAAOK,MAAM,CAACE,YAAY,CAAC,sBAAsB,CAAC;IACpD;EACF,CAAC,CAAC,MAAM;IACN,MAAMC,cAAc,GAClB,CAAAf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,OAAO,KAAIhB,WAAW,CAACgB,OAAO,CAACC,MAAM,GAAG,CAAC,GACjD,GAAEjB,WAAW,CAACgB,OAAO,CAACE,GAAG,CAAEC,IAAI,IAAKC,gBAAK,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC,CAACT,IAAI,CAAC,IAAI,CAAE,EAAC,GACnE,YAAY;IAElB,MAAM,KAAIY,oBAAQ,EACf,wCAAuCzB,MAAO,0CAAyCkB,cAAe,0EAAyE,CACjL;EACH;EAEA,OAAOjB,aAAa;AACtB"}