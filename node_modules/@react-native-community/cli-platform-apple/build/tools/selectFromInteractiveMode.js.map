{"version": 3, "names": ["selectFromInteractiveMode", "scheme", "mode", "info", "newScheme", "newMode", "schemes", "length", "promptForSchemeSelection", "logger", "chalk", "bold", "configurations", "promptForConfigurationSelection"], "sources": ["../../src/tools/selectFromInteractiveMode.ts"], "sourcesContent": ["import {logger} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport {IosInfo} from '../types';\nimport {\n  promptForConfigurationSelection,\n  promptForSchemeSelection,\n} from './prompts';\n\ninterface Args {\n  scheme?: string;\n  mode?: string;\n  info: IosInfo | undefined;\n}\n\nexport async function selectFromInteractiveMode({\n  scheme,\n  mode,\n  info,\n}: Args): Promise<{scheme?: string; mode?: string}> {\n  let newScheme = scheme;\n  let newMode = mode;\n\n  const schemes = info?.schemes;\n  if (schemes && schemes.length > 1) {\n    newScheme = await promptForSchemeSelection(schemes);\n  } else {\n    logger.info(`Automatically selected ${chalk.bold(scheme)} scheme.`);\n  }\n\n  const configurations = info?.configurations;\n  if (configurations && configurations.length > 1) {\n    newMode = await promptForConfigurationSelection(configurations);\n  } else {\n    logger.info(`Automatically selected ${chalk.bold(mode)} configuration.`);\n  }\n\n  return {\n    scheme: newScheme,\n    mode: newMode,\n  };\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AAGmB;AAQZ,eAAeA,yBAAyB,CAAC;EAC9CC,MAAM;EACNC,IAAI;EACJC;AACI,CAAC,EAA6C;EAClD,IAAIC,SAAS,GAAGH,MAAM;EACtB,IAAII,OAAO,GAAGH,IAAI;EAElB,MAAMI,OAAO,GAAGH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,OAAO;EAC7B,IAAIA,OAAO,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IACjCH,SAAS,GAAG,MAAM,IAAAI,iCAAwB,EAACF,OAAO,CAAC;EACrD,CAAC,MAAM;IACLG,kBAAM,CAACN,IAAI,CAAE,0BAAyBO,gBAAK,CAACC,IAAI,CAACV,MAAM,CAAE,UAAS,CAAC;EACrE;EAEA,MAAMW,cAAc,GAAGT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,cAAc;EAC3C,IAAIA,cAAc,IAAIA,cAAc,CAACL,MAAM,GAAG,CAAC,EAAE;IAC/CF,OAAO,GAAG,MAAM,IAAAQ,wCAA+B,EAACD,cAAc,CAAC;EACjE,CAAC,MAAM;IACLH,kBAAM,CAACN,IAAI,CAAE,0BAAyBO,gBAAK,CAACC,IAAI,CAACT,IAAI,CAAE,iBAAgB,CAAC;EAC1E;EAEA,OAAO;IACLD,MAAM,EAAEG,SAAS;IACjBF,IAAI,EAAEG;EACR,CAAC;AACH"}