{"version": 3, "names": ["getArchitecture", "iosSourceDir", "project", "readFile", "path", "join", "includes"], "sources": ["../../src/tools/getArchitecture.ts"], "sourcesContent": ["import {readFile} from 'fs-extra';\nimport path from 'path';\n\nexport default async function getArchitecture(iosSourceDir: string) {\n  try {\n    const project = await readFile(\n      path.join(iosSourceDir, '/Pods/Pods.xcodeproj/project.pbxproj'),\n    );\n\n    return project.includes('-DRCT_NEW_ARCH_ENABLED=1');\n  } catch {\n    return false;\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAET,eAAeA,eAAe,CAACC,YAAoB,EAAE;EAClE,IAAI;IACF,MAAMC,OAAO,GAAG,MAAM,IAAAC,mBAAQ,EAC5BC,eAAI,CAACC,IAAI,CAACJ,YAAY,EAAE,sCAAsC,CAAC,CAChE;IAED,OAAOC,OAAO,CAACI,QAAQ,CAAC,0BAA0B,CAAC;EACrD,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF"}