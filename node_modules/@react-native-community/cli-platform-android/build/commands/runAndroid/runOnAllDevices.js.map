{"version": 3, "names": ["runOnAllDevices", "args", "cmd", "adbPath", "androidProject", "devices", "adb", "getDevices", "length", "logger", "info", "result", "tryLaunchEmulator", "success", "error", "chalk", "dim", "warn", "binaryPath", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "mode", "tasks", "extraParams", "push", "port", "activeArchOnly", "architectures", "map", "device", "getCPU", "filter", "arch", "index", "array", "indexOf", "join", "debug", "execa", "stdio", "cwd", "sourceDir", "printRunDoctorTip", "createInstallError", "undefined", "for<PERSON>ach", "tryRunAdbReverse", "tryInstallAppOnDevice", "tryLaunchAppOnDevice", "stderr", "toString", "message", "log", "includes", "bold", "underline", "link", "docs", "hash", "guide", "CLIError"], "sources": ["../../../src/commands/runAndroid/runOnAllDevices.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport chalk from 'chalk';\nimport execa from 'execa';\nimport {Config} from '@react-native-community/cli-types';\nimport {\n  link,\n  logger,\n  CLIError,\n  printRunDoctorTip,\n} from '@react-native-community/cli-tools';\nimport adb from './adb';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport tryLaunchEmulator from './tryLaunchEmulator';\nimport tryInstallAppOnDevice from './tryInstallAppOnDevice';\nimport {getTaskNames} from './getTaskNames';\nimport type {Flags} from '.';\n\ntype AndroidProject = NonNullable<Config['project']['android']>;\n\nasync function runOnAllDevices(\n  args: Flags,\n  cmd: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n) {\n  let devices = adb.getDevices(adbPath);\n  if (devices.length === 0) {\n    logger.info('Launching emulator...');\n    const result = await tryLaunchEmulator(adbPath);\n    if (result.success) {\n      logger.info('Successfully launched emulator.');\n      devices = adb.getDevices(adbPath);\n    } else {\n      logger.error(\n        `Failed to launch emulator. Reason: ${chalk.dim(result.error || '')}.`,\n      );\n      logger.warn(\n        'Please launch an emulator manually or connect a device. Otherwise app may fail to launch.',\n      );\n    }\n  }\n\n  try {\n    if (!args.binaryPath) {\n      let gradleArgs = getTaskNames(\n        androidProject.appName,\n        args.mode,\n        args.tasks,\n        'install',\n      );\n\n      if (args.extraParams) {\n        gradleArgs.push(...args.extraParams);\n      }\n\n      if (args.port != null) {\n        gradleArgs.push('-PreactNativeDevServerPort=' + args.port);\n      }\n\n      if (args.activeArchOnly) {\n        const architectures = devices\n          .map((device) => {\n            return adb.getCPU(adbPath, device);\n          })\n          .filter(\n            (arch, index, array) =>\n              arch != null && array.indexOf(arch) === index,\n          );\n\n        if (architectures.length > 0) {\n          logger.info(`Detected architectures ${architectures.join(', ')}`);\n          gradleArgs.push(\n            '-PreactNativeArchitectures=' + architectures.join(','),\n          );\n        }\n      }\n\n      logger.info('Installing the app...');\n      logger.debug(\n        `Running command \"cd android && ${cmd} ${gradleArgs.join(' ')}\"`,\n      );\n\n      await execa(cmd, gradleArgs, {\n        stdio: ['inherit', 'inherit', 'pipe'],\n        cwd: androidProject.sourceDir,\n      });\n    }\n  } catch (error) {\n    printRunDoctorTip();\n    throw createInstallError(error as any);\n  }\n\n  (devices.length > 0 ? devices : [undefined]).forEach(\n    (device: string | void) => {\n      tryRunAdbReverse(args.port, device);\n      if (args.binaryPath && device) {\n        tryInstallAppOnDevice(args, adbPath, device, androidProject);\n      }\n      tryLaunchAppOnDevice(device, androidProject, adbPath, args);\n    },\n  );\n}\n\nfunction createInstallError(error: Error & {stderr: string}) {\n  const stderr = (error.stderr || '').toString();\n  let message = '';\n  // Pass the error message from the command to stdout because we pipe it to\n  // parent process so it's not visible\n  logger.log(stderr);\n\n  // Handle some common failures and make the errors more helpful\n  if (stderr.includes('No connected devices')) {\n    message =\n      'Make sure you have an Android emulator running or a device connected.';\n  } else if (\n    stderr.includes('licences have not been accepted') ||\n    stderr.includes('accept the SDK license')\n  ) {\n    message = `Please accept all necessary Android SDK licenses using Android SDK Manager: \"${chalk.bold(\n      '$ANDROID_HOME/tools/bin/sdkmanager --licenses',\n    )}.\"`;\n  } else if (stderr.includes('requires Java')) {\n    message = `Looks like your Android environment is not properly set. Please go to ${chalk.dim.underline(\n      link.docs('environment-setup', 'android', {\n        hash: 'jdk-studio',\n        guide: 'native',\n      }),\n    )} and follow the React Native CLI QuickStart guide to install the compatible version of JDK.`;\n  } else {\n    message = error.message;\n  }\n\n  return new CLIError(\n    `Failed to install the app.${message ? ' ' + message : ''}`,\n    error.message.length > 0 ? undefined : error,\n  );\n}\n\nexport default runOnAllDevices;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AAA4C;AAtB5C;AACA;AACA;AACA;AACA;AACA;AACA;;AAqBA,eAAeA,eAAe,CAC5BC,IAAW,EACXC,GAAW,EACXC,OAAe,EACfC,cAA8B,EAC9B;EACA,IAAIC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACrC,IAAIE,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;IACxBC,kBAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;IACpC,MAAMC,MAAM,GAAG,MAAM,IAAAC,0BAAiB,EAACT,OAAO,CAAC;IAC/C,IAAIQ,MAAM,CAACE,OAAO,EAAE;MAClBJ,kBAAM,CAACC,IAAI,CAAC,iCAAiC,CAAC;MAC9CL,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;IACnC,CAAC,MAAM;MACLM,kBAAM,CAACK,KAAK,CACT,sCAAqCC,gBAAK,CAACC,GAAG,CAACL,MAAM,CAACG,KAAK,IAAI,EAAE,CAAE,GAAE,CACvE;MACDL,kBAAM,CAACQ,IAAI,CACT,2FAA2F,CAC5F;IACH;EACF;EAEA,IAAI;IACF,IAAI,CAAChB,IAAI,CAACiB,UAAU,EAAE;MACpB,IAAIC,UAAU,GAAG,IAAAC,0BAAY,EAC3BhB,cAAc,CAACiB,OAAO,EACtBpB,IAAI,CAACqB,IAAI,EACTrB,IAAI,CAACsB,KAAK,EACV,SAAS,CACV;MAED,IAAItB,IAAI,CAACuB,WAAW,EAAE;QACpBL,UAAU,CAACM,IAAI,CAAC,GAAGxB,IAAI,CAACuB,WAAW,CAAC;MACtC;MAEA,IAAIvB,IAAI,CAACyB,IAAI,IAAI,IAAI,EAAE;QACrBP,UAAU,CAACM,IAAI,CAAC,6BAA6B,GAAGxB,IAAI,CAACyB,IAAI,CAAC;MAC5D;MAEA,IAAIzB,IAAI,CAAC0B,cAAc,EAAE;QACvB,MAAMC,aAAa,GAAGvB,OAAO,CAC1BwB,GAAG,CAAEC,MAAM,IAAK;UACf,OAAOxB,YAAG,CAACyB,MAAM,CAAC5B,OAAO,EAAE2B,MAAM,CAAC;QACpC,CAAC,CAAC,CACDE,MAAM,CACL,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KACjBF,IAAI,IAAI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,CAChD;QAEH,IAAIN,aAAa,CAACpB,MAAM,GAAG,CAAC,EAAE;UAC5BC,kBAAM,CAACC,IAAI,CAAE,0BAAyBkB,aAAa,CAACS,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;UACjElB,UAAU,CAACM,IAAI,CACb,6BAA6B,GAAGG,aAAa,CAACS,IAAI,CAAC,GAAG,CAAC,CACxD;QACH;MACF;MAEA5B,kBAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;MACpCD,kBAAM,CAAC6B,KAAK,CACT,kCAAiCpC,GAAI,IAAGiB,UAAU,CAACkB,IAAI,CAAC,GAAG,CAAE,GAAE,CACjE;MAED,MAAM,IAAAE,gBAAK,EAACrC,GAAG,EAAEiB,UAAU,EAAE;QAC3BqB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;QACrCC,GAAG,EAAErC,cAAc,CAACsC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;IACd,IAAA6B,6BAAiB,GAAE;IACnB,MAAMC,kBAAkB,CAAC9B,KAAK,CAAQ;EACxC;EAEA,CAACT,OAAO,CAACG,MAAM,GAAG,CAAC,GAAGH,OAAO,GAAG,CAACwC,SAAS,CAAC,EAAEC,OAAO,CACjDhB,MAAqB,IAAK;IACzB,IAAAiB,yBAAgB,EAAC9C,IAAI,CAACyB,IAAI,EAAEI,MAAM,CAAC;IACnC,IAAI7B,IAAI,CAACiB,UAAU,IAAIY,MAAM,EAAE;MAC7B,IAAAkB,8BAAqB,EAAC/C,IAAI,EAAEE,OAAO,EAAE2B,MAAM,EAAE1B,cAAc,CAAC;IAC9D;IACA,IAAA6C,6BAAoB,EAACnB,MAAM,EAAE1B,cAAc,EAAED,OAAO,EAAEF,IAAI,CAAC;EAC7D,CAAC,CACF;AACH;AAEA,SAAS2C,kBAAkB,CAAC9B,KAA+B,EAAE;EAC3D,MAAMoC,MAAM,GAAG,CAACpC,KAAK,CAACoC,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,OAAO,GAAG,EAAE;EAChB;EACA;EACA3C,kBAAM,CAAC4C,GAAG,CAACH,MAAM,CAAC;;EAElB;EACA,IAAIA,MAAM,CAACI,QAAQ,CAAC,sBAAsB,CAAC,EAAE;IAC3CF,OAAO,GACL,uEAAuE;EAC3E,CAAC,MAAM,IACLF,MAAM,CAACI,QAAQ,CAAC,iCAAiC,CAAC,IAClDJ,MAAM,CAACI,QAAQ,CAAC,wBAAwB,CAAC,EACzC;IACAF,OAAO,GAAI,gFAA+ErC,gBAAK,CAACwC,IAAI,CAClG,+CAA+C,CAC/C,IAAG;EACP,CAAC,MAAM,IAAIL,MAAM,CAACI,QAAQ,CAAC,eAAe,CAAC,EAAE;IAC3CF,OAAO,GAAI,yEAAwErC,gBAAK,CAACC,GAAG,CAACwC,SAAS,CACpGC,gBAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE,SAAS,EAAE;MACxCC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE;IACT,CAAC,CAAC,CACF,6FAA4F;EAChG,CAAC,MAAM;IACLR,OAAO,GAAGtC,KAAK,CAACsC,OAAO;EACzB;EAEA,OAAO,KAAIS,oBAAQ,EAChB,6BAA4BT,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG,EAAG,EAAC,EAC3DtC,KAAK,CAACsC,OAAO,CAAC5C,MAAM,GAAG,CAAC,GAAGqC,SAAS,GAAG/B,KAAK,CAC7C;AACH;AAAC,eAEcd,eAAe;AAAA"}