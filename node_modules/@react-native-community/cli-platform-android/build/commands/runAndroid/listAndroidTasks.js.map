{"version": 3, "names": ["parseTasksFromGradleFile", "taskType", "text", "instalTasks", "taskRegex", "RegExp", "split", "for<PERSON>ach", "line", "test", "trim", "metadata", "push", "task", "description", "getGradleTasks", "sourceDir", "loader", "<PERSON><PERSON><PERSON><PERSON>", "start", "cmd", "process", "platform", "startsWith", "out", "execa", "sync", "cwd", "stdout", "succeed", "fail", "promptForTaskSelection", "tasks", "length", "CLIError", "prompt", "type", "name", "message", "choices", "map", "t", "title", "chalk", "bold", "value", "min"], "sources": ["../../../src/commands/runAndroid/listAndroidTasks.ts"], "sourcesContent": ["import {CLIError, getLoader, prompt} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\nimport execa from 'execa';\n\ntype GradleTask = {\n  task: string;\n  description: string;\n};\n\nexport const parseTasksFromGradleFile = (\n  taskType: 'install' | 'build',\n  text: string,\n): Array<GradleTask> => {\n  const instalTasks: Array<GradleTask> = [];\n  const taskRegex = new RegExp(\n    taskType === 'build' ? '^assemble|^bundle' : '^install',\n  );\n  text.split('\\n').forEach((line) => {\n    if (taskRegex.test(line.trim()) && /(?!.*?Test)^.*$/.test(line.trim())) {\n      const metadata = line.split(' - ');\n      instalTasks.push({\n        task: metadata[0],\n        description: metadata[1],\n      });\n    }\n  });\n  return instalTasks;\n};\n\nexport const getGradleTasks = (\n  taskType: 'install' | 'build',\n  sourceDir: string,\n) => {\n  const loader = getLoader();\n  loader.start('Searching for available Gradle tasks...');\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n  try {\n    const out = execa.sync(cmd, ['tasks', '--group', taskType], {\n      cwd: sourceDir,\n    }).stdout;\n    loader.succeed();\n    return parseTasksFromGradleFile(taskType, out);\n  } catch {\n    loader.fail();\n    return [];\n  }\n};\n\nexport const promptForTaskSelection = async (\n  taskType: 'install' | 'build',\n  sourceDir: string,\n): Promise<string | undefined> => {\n  const tasks = getGradleTasks(taskType, sourceDir);\n  if (!tasks.length) {\n    throw new CLIError(`No actionable ${taskType} tasks were found...`);\n  }\n  const {task}: {task: string} = await prompt({\n    type: 'select',\n    name: 'task',\n    message: `Select ${taskType} task you want to perform`,\n    choices: tasks.map((t: GradleTask) => ({\n      title: `${chalk.bold(t.task)} - ${t.description}`,\n      value: t.task,\n    })),\n    min: 1,\n  });\n  return task;\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAOnB,MAAMA,wBAAwB,GAAG,CACtCC,QAA6B,EAC7BC,IAAY,KACU;EACtB,MAAMC,WAA8B,GAAG,EAAE;EACzC,MAAMC,SAAS,GAAG,IAAIC,MAAM,CAC1BJ,QAAQ,KAAK,OAAO,GAAG,mBAAmB,GAAG,UAAU,CACxD;EACDC,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;IACjC,IAAIJ,SAAS,CAACK,IAAI,CAACD,IAAI,CAACE,IAAI,EAAE,CAAC,IAAI,iBAAiB,CAACD,IAAI,CAACD,IAAI,CAACE,IAAI,EAAE,CAAC,EAAE;MACtE,MAAMC,QAAQ,GAAGH,IAAI,CAACF,KAAK,CAAC,KAAK,CAAC;MAClCH,WAAW,CAACS,IAAI,CAAC;QACfC,IAAI,EAAEF,QAAQ,CAAC,CAAC,CAAC;QACjBG,WAAW,EAAEH,QAAQ,CAAC,CAAC;MACzB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOR,WAAW;AACpB,CAAC;AAAC;AAEK,MAAMY,cAAc,GAAG,CAC5Bd,QAA6B,EAC7Be,SAAiB,KACd;EACH,MAAMC,MAAM,GAAG,IAAAC,qBAAS,GAAE;EAC1BD,MAAM,CAACE,KAAK,CAAC,yCAAyC,CAAC;EACvD,MAAMC,GAAG,GAAGC,OAAO,CAACC,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;EAC5E,IAAI;IACF,MAAMC,GAAG,GAAGC,gBAAK,CAACC,IAAI,CAACN,GAAG,EAAE,CAAC,OAAO,EAAE,SAAS,EAAEnB,QAAQ,CAAC,EAAE;MAC1D0B,GAAG,EAAEX;IACP,CAAC,CAAC,CAACY,MAAM;IACTX,MAAM,CAACY,OAAO,EAAE;IAChB,OAAO7B,wBAAwB,CAACC,QAAQ,EAAEuB,GAAG,CAAC;EAChD,CAAC,CAAC,MAAM;IACNP,MAAM,CAACa,IAAI,EAAE;IACb,OAAO,EAAE;EACX;AACF,CAAC;AAAC;AAEK,MAAMC,sBAAsB,GAAG,OACpC9B,QAA6B,EAC7Be,SAAiB,KACe;EAChC,MAAMgB,KAAK,GAAGjB,cAAc,CAACd,QAAQ,EAAEe,SAAS,CAAC;EACjD,IAAI,CAACgB,KAAK,CAACC,MAAM,EAAE;IACjB,MAAM,KAAIC,oBAAQ,EAAE,iBAAgBjC,QAAS,sBAAqB,CAAC;EACrE;EACA,MAAM;IAACY;EAAoB,CAAC,GAAG,MAAM,IAAAsB,kBAAM,EAAC;IAC1CC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAG,UAASrC,QAAS,2BAA0B;IACtDsC,OAAO,EAAEP,KAAK,CAACQ,GAAG,CAAEC,CAAa,KAAM;MACrCC,KAAK,EAAG,GAAEC,gBAAK,CAACC,IAAI,CAACH,CAAC,CAAC5B,IAAI,CAAE,MAAK4B,CAAC,CAAC3B,WAAY,EAAC;MACjD+B,KAAK,EAAEJ,CAAC,CAAC5B;IACX,CAAC,CAAC,CAAC;IACHiC,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOjC,IAAI;AACb,CAAC;AAAC"}