{"name": "pretty-format", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/pretty-format"}, "license": "MIT", "description": "Stringify any JavaScript value.", "main": "build/index.js", "types": "build/index.d.ts", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@jest/types": "^26.6.2", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^17.0.1"}, "devDependencies": {"@types/react": "*", "@types/react-is": "^16.7.1", "@types/react-test-renderer": "*", "immutable": "4.0.0-rc.9", "jest-util": "^26.6.2", "react": "*", "react-dom": "*", "react-test-renderer": "*"}, "engines": {"node": ">= 10"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}