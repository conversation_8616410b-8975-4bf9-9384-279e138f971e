{"name": "@react-native-community/cli-server-api", "version": "18.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-tools": "18.0.0", "body-parser": "^1.20.3", "compression": "^1.7.1", "connect": "^3.6.5", "errorhandler": "^1.5.1", "nocache": "^3.0.1", "open": "^6.2.0", "pretty-format": "^26.6.2", "serve-static": "^1.13.1", "ws": "^6.2.3"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/connect": "^3.4.33", "@types/errorhandler": "^1.5.0", "@types/ws": "^7.4.7"}, "files": ["build", "!*.map"], "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-server-api", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-server-api"}, "gitHead": "f50c1f19a8068787d074560375b726d89c30a088"}