/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import connect from 'connect';
type Options = {
    watchFolders: ReadonlyArray<string>;
};
/**
 * Open a stack frame in the user's text editor.
 */
export default function openStackFrameMiddleware(_: Options): connect.Server;
export {};
//# sourceMappingURL=openStackFrameMiddleware.d.ts.map