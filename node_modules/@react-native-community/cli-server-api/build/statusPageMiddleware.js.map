{"version": 3, "names": ["statusPageMiddleware", "_req", "res", "<PERSON><PERSON><PERSON><PERSON>", "process", "cwd", "end"], "sources": ["../src/statusPageMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\n\n/**\n * Status page so that anyone who needs to can verify that the packager is\n * running on 8081 and not another program / service.\n */\nexport default function statusPageMiddleware(\n  _req: http.IncomingMessage,\n  res: http.ServerResponse,\n) {\n  res.setHeader('X-React-Native-Project-Root', process.cwd());\n  res.end('packager-status:running');\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACe,SAASA,oBAAoB,CAC1CC,IAA0B,EAC1BC,GAAwB,EACxB;EACAA,GAAG,CAACC,SAAS,CAAC,6BAA6B,EAAEC,OAAO,CAACC,GAAG,EAAE,CAAC;EAC3DH,GAAG,CAACI,GAAG,CAAC,yBAAyB,CAAC;AACpC"}