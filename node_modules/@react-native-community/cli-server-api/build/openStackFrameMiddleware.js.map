{"version": 3, "names": ["openStackFrameMiddleware", "_", "handler", "req", "res", "next", "method", "body", "writeHead", "end", "frame", "launchEditor", "file", "lineNumber", "connect", "use", "json"], "sources": ["../src/openStackFrameMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type {IncomingMessage, ServerResponse} from 'http';\n\nimport {json} from 'body-parser';\nimport connect from 'connect';\nimport {launchEditor} from '@react-native-community/cli-tools';\n\ntype Options = {\n  watchFolders: ReadonlyArray<string>;\n};\n\n/**\n * Open a stack frame in the user's text editor.\n */\nexport default function openStackFrameMiddleware(_: Options) {\n  const handler = (\n    req: IncomingMessage & {\n      // Populated by body-parser\n      body?: Object;\n    },\n    res: ServerResponse,\n    next: (err?: Error) => void,\n  ) => {\n    if (req.method === 'POST') {\n      if (req.body == null) {\n        res.writeHead(400);\n        res.end('Missing request body');\n        return;\n      }\n\n      const frame = req.body as {\n        file: string;\n        lineNumber: number;\n      };\n\n      launchEditor(frame.file, frame.lineNumber);\n\n      res.writeHead(200);\n      res.end();\n    }\n\n    next();\n  };\n\n  return connect().use(json()).use(handler);\n}\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAX/D;AACA;AACA;AACA;AACA;AACA;;AAYA;AACA;AACA;AACe,SAASA,wBAAwB,CAACC,CAAU,EAAE;EAC3D,MAAMC,OAAO,GAAG,CACdC,GAGC,EACDC,GAAmB,EACnBC,IAA2B,KACxB;IACH,IAAIF,GAAG,CAACG,MAAM,KAAK,MAAM,EAAE;MACzB,IAAIH,GAAG,CAACI,IAAI,IAAI,IAAI,EAAE;QACpBH,GAAG,CAACI,SAAS,CAAC,GAAG,CAAC;QAClBJ,GAAG,CAACK,GAAG,CAAC,sBAAsB,CAAC;QAC/B;MACF;MAEA,MAAMC,KAAK,GAAGP,GAAG,CAACI,IAGjB;MAED,IAAAI,wBAAY,EAACD,KAAK,CAACE,IAAI,EAAEF,KAAK,CAACG,UAAU,CAAC;MAE1CT,GAAG,CAACI,SAAS,CAAC,GAAG,CAAC;MAClBJ,GAAG,CAACK,GAAG,EAAE;IACX;IAEAJ,IAAI,EAAE;EACR,CAAC;EAED,OAAO,IAAAS,kBAAO,GAAE,CAACC,GAAG,CAAC,IAAAC,kBAAI,GAAE,CAAC,CAACD,GAAG,CAACb,OAAO,CAAC;AAC3C"}