"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _bodyParser() {
  const data = require("body-parser");
  _bodyParser = function () {
    return data;
  };
  return data;
}
function _connect() {
  const data = _interopRequireDefault(require("connect"));
  _connect = function () {
    return data;
  };
  return data;
}
function _open() {
  const data = _interopRequireDefault(require("open"));
  _open = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * Open a URL in the system browser.
 */
async function openURLMiddleware(req, res, next) {
  if (req.method === 'POST') {
    if (req.body == null) {
      res.writeHead(400);
      res.end('Missing request body');
      return;
    }
    const {
      url
    } = req.body;
    await (0, _open().default)(url);
    res.writeHead(200);
    res.end();
  }
  next();
}
var _default = (0, _connect().default)().use((0, _bodyParser().json)()).use(openURLMiddleware);
exports.default = _default;

//# sourceMappingURL=/Users/<USER>/Developer/oss/rncli/packages/cli-server-api/build/openURLMiddleware.js.map