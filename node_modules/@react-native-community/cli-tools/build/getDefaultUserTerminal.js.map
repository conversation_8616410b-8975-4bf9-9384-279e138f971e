{"version": 3, "names": ["getDefaultUserTerminal", "REACT_TERMINAL", "TERM_PROGRAM", "TERM", "process", "env", "os", "platform"], "sources": ["../src/getDefaultUserTerminal.ts"], "sourcesContent": ["import os from 'os';\n\nconst getDefaultUserTerminal = (): string | undefined => {\n  const {REACT_TERMINAL, TERM_PROGRAM, TERM} = process.env;\n\n  if (REACT_TERMINAL) {\n    return REACT_TERMINAL;\n  }\n\n  if (os.platform() === 'darwin') {\n    return TERM_PROGRAM;\n  }\n\n  if (os.platform() === 'win32') {\n    return 'cmd.exe';\n  }\n\n  return TERM;\n};\n\nexport default getDefaultUserTerminal;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAEpB,MAAMA,sBAAsB,GAAG,MAA0B;EACvD,MAAM;IAACC,cAAc;IAAEC,YAAY;IAAEC;EAAI,CAAC,GAAGC,OAAO,CAACC,GAAG;EAExD,IAAIJ,cAAc,EAAE;IAClB,OAAOA,cAAc;EACvB;EAEA,IAAIK,aAAE,CAACC,QAAQ,EAAE,KAAK,QAAQ,EAAE;IAC9B,OAAOL,YAAY;EACrB;EAEA,IAAII,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,EAAE;IAC7B,OAAO,SAAS;EAClB;EAEA,OAAOJ,IAAI;AACb,CAAC;AAAC,eAEaH,sBAAsB;AAAA"}