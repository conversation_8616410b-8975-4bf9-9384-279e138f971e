{"version": 3, "names": ["isDiffPurgeEntry", "data", "name", "zipball_url", "tarball_url", "commit", "sha", "url", "node_id", "indexOf", "getLatestRelease", "currentVersion", "logger", "debug", "some", "s", "includes", "cachedLatest", "cacheManager", "get", "eTag", "stable", "candidate", "getLatestRnDiffPurgeVersion", "semver", "compare", "changelogUrl", "buildChangelogUrl", "diffUrl", "buildDiffUrl", "e", "undefined", "version", "oldVersion", "newVersion", "options", "headers", "status", "fetch", "result", "body", "filter", "eTagHeader", "substring", "set"], "sources": ["../../src/releaseChecker/getLatestRelease.ts"], "sourcesContent": ["import semver from 'semver';\nimport cacheManager from '../cacheManager';\nimport {fetch} from '../fetch';\nimport logger from '../logger';\n\nexport type Release = {\n  // The current stable release\n  stable: string;\n  // The current candidate release. These are only populated if the latest release is a candidate release.\n  candidate?: string;\n  changelogUrl: string;\n  diffUrl: string;\n};\n\ninterface DiffPurge {\n  name: string;\n  zipball_url: string;\n  tarball_url: string;\n  commit: {\n    sha: string;\n    url: string;\n  };\n  node_id: string;\n}\n\nfunction isDiffPurgeEntry(data: any): data is DiffPurge {\n  return (\n    [\n      data.name,\n      data.zipball_url,\n      data.tarball_url,\n      data.commit?.sha,\n      data.commit?.url,\n      data.node_id,\n    ].indexOf(false) === -1\n  );\n}\n\n/**\n * Checks via GitHub API if there is a newer stable React Native release and,\n * if it exists, returns the release data.\n *\n * If the latest release is not newer or if it's a prerelease, the function\n * will return undefined.\n */\nexport default async function getLatestRelease(\n  name: string,\n  currentVersion: string,\n): Promise<Release | undefined> {\n  logger.debug('Checking for a newer version of React Native');\n  try {\n    logger.debug(`Current version: ${currentVersion}`);\n\n    // if the version is a nightly/canary build, we want to bail\n    // since they are nightlies or unreleased versions\n    if (['-canary', '-nightly'].some((s) => currentVersion.includes(s))) {\n      return;\n    }\n\n    const cachedLatest = cacheManager.get(name, 'latestVersion');\n\n    if (cachedLatest) {\n      logger.debug(`Cached release version: ${cachedLatest}`);\n    }\n\n    logger.debug('Checking for newer releases on GitHub');\n    const eTag = cacheManager.get(name, 'eTag');\n    const {stable, candidate} = await getLatestRnDiffPurgeVersion(name, eTag);\n    logger.debug(`Latest release: ${stable} (${candidate})`);\n\n    if (semver.compare(stable, currentVersion) >= 0) {\n      return {\n        stable,\n        candidate,\n        changelogUrl: buildChangelogUrl(stable),\n        diffUrl: buildDiffUrl(currentVersion, stable),\n      };\n    }\n  } catch (e) {\n    logger.debug(\n      'Something went wrong with remote version checking, moving on',\n    );\n    logger.debug(e as any);\n  }\n  return undefined;\n}\n\nfunction buildChangelogUrl(version: string) {\n  return `https://github.com/facebook/react-native/releases/tag/v${version}`;\n}\n\nfunction buildDiffUrl(oldVersion: string, newVersion: string) {\n  return `https://react-native-community.github.io/upgrade-helper/?from=${oldVersion}&to=${newVersion}`;\n}\n\ntype LatestVersions = {\n  candidate?: string;\n  stable: string;\n};\n\n/**\n * Returns the most recent React Native version available to upgrade to.\n */\nasync function getLatestRnDiffPurgeVersion(\n  name: string,\n  eTag?: string,\n): Promise<LatestVersions> {\n  const options = {\n    // https://developer.github.com/v3/#user-agent-required\n    headers: {'User-Agent': 'React-Native-CLI'} as Headers,\n  };\n\n  if (eTag) {\n    options.headers['If-None-Match'] = eTag;\n  }\n\n  const {data, status, headers} = await fetch(\n    'https://api.github.com/repos/react-native-community/rn-diff-purge/tags',\n    options,\n  );\n\n  const result: LatestVersions = {stable: '0.0.0'};\n\n  // Remote is newer.\n  if (status === 200) {\n    const body: DiffPurge[] = data.filter(isDiffPurgeEntry);\n    const eTagHeader = headers.get('eTag');\n\n    for (let {name: version} of body) {\n      if (!result.candidate && version.includes('-rc')) {\n        result.candidate = version.substring(8);\n        continue;\n      }\n      if (!version.includes('-rc')) {\n        result.stable = version.substring(8);\n        if (eTagHeader) {\n          logger.debug(`Saving ${eTagHeader} to cache`);\n          cacheManager.set(name, 'eTag', eTagHeader);\n          cacheManager.set(name, 'latestVersion', result.stable);\n        }\n        return result;\n      }\n    }\n    return result;\n  }\n\n  // Cache is still valid.\n  if (status === 304) {\n    result.stable = cacheManager.get(name, 'latestVersion') ?? result.stable;\n  }\n\n  // Should be returned only if something went wrong.\n  return result;\n}\n\ntype Headers = {\n  'User-Agent': string;\n  [header: string]: string;\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAA+B;AAsB/B,SAASA,gBAAgB,CAACC,IAAS,EAAqB;EAAA;EACtD,OACE,CACEA,IAAI,CAACC,IAAI,EACTD,IAAI,CAACE,WAAW,EAChBF,IAAI,CAACG,WAAW,kBAChBH,IAAI,CAACI,MAAM,iDAAX,aAAaC,GAAG,mBAChBL,IAAI,CAACI,MAAM,kDAAX,cAAaE,GAAG,EAChBN,IAAI,CAACO,OAAO,CACb,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAE3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,eAAeC,gBAAgB,CAC5CR,IAAY,EACZS,cAAsB,EACQ;EAC9BC,eAAM,CAACC,KAAK,CAAC,8CAA8C,CAAC;EAC5D,IAAI;IACFD,eAAM,CAACC,KAAK,CAAE,oBAAmBF,cAAe,EAAC,CAAC;;IAElD;IACA;IACA,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAACG,IAAI,CAAEC,CAAC,IAAKJ,cAAc,CAACK,QAAQ,CAACD,CAAC,CAAC,CAAC,EAAE;MACnE;IACF;IAEA,MAAME,YAAY,GAAGC,qBAAY,CAACC,GAAG,CAACjB,IAAI,EAAE,eAAe,CAAC;IAE5D,IAAIe,YAAY,EAAE;MAChBL,eAAM,CAACC,KAAK,CAAE,2BAA0BI,YAAa,EAAC,CAAC;IACzD;IAEAL,eAAM,CAACC,KAAK,CAAC,uCAAuC,CAAC;IACrD,MAAMO,IAAI,GAAGF,qBAAY,CAACC,GAAG,CAACjB,IAAI,EAAE,MAAM,CAAC;IAC3C,MAAM;MAACmB,MAAM;MAAEC;IAAS,CAAC,GAAG,MAAMC,2BAA2B,CAACrB,IAAI,EAAEkB,IAAI,CAAC;IACzER,eAAM,CAACC,KAAK,CAAE,mBAAkBQ,MAAO,KAAIC,SAAU,GAAE,CAAC;IAExD,IAAIE,iBAAM,CAACC,OAAO,CAACJ,MAAM,EAAEV,cAAc,CAAC,IAAI,CAAC,EAAE;MAC/C,OAAO;QACLU,MAAM;QACNC,SAAS;QACTI,YAAY,EAAEC,iBAAiB,CAACN,MAAM,CAAC;QACvCO,OAAO,EAAEC,YAAY,CAAClB,cAAc,EAAEU,MAAM;MAC9C,CAAC;IACH;EACF,CAAC,CAAC,OAAOS,CAAC,EAAE;IACVlB,eAAM,CAACC,KAAK,CACV,8DAA8D,CAC/D;IACDD,eAAM,CAACC,KAAK,CAACiB,CAAC,CAAQ;EACxB;EACA,OAAOC,SAAS;AAClB;AAEA,SAASJ,iBAAiB,CAACK,OAAe,EAAE;EAC1C,OAAQ,0DAAyDA,OAAQ,EAAC;AAC5E;AAEA,SAASH,YAAY,CAACI,UAAkB,EAAEC,UAAkB,EAAE;EAC5D,OAAQ,iEAAgED,UAAW,OAAMC,UAAW,EAAC;AACvG;AAOA;AACA;AACA;AACA,eAAeX,2BAA2B,CACxCrB,IAAY,EACZkB,IAAa,EACY;EACzB,MAAMe,OAAO,GAAG;IACd;IACAC,OAAO,EAAE;MAAC,YAAY,EAAE;IAAkB;EAC5C,CAAC;EAED,IAAIhB,IAAI,EAAE;IACRe,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAGhB,IAAI;EACzC;EAEA,MAAM;IAACnB,IAAI;IAAEoC,MAAM;IAAED;EAAO,CAAC,GAAG,MAAM,IAAAE,YAAK,EACzC,wEAAwE,EACxEH,OAAO,CACR;EAED,MAAMI,MAAsB,GAAG;IAAClB,MAAM,EAAE;EAAO,CAAC;;EAEhD;EACA,IAAIgB,MAAM,KAAK,GAAG,EAAE;IAClB,MAAMG,IAAiB,GAAGvC,IAAI,CAACwC,MAAM,CAACzC,gBAAgB,CAAC;IACvD,MAAM0C,UAAU,GAAGN,OAAO,CAACjB,GAAG,CAAC,MAAM,CAAC;IAEtC,KAAK,IAAI;MAACjB,IAAI,EAAE8B;IAAO,CAAC,IAAIQ,IAAI,EAAE;MAChC,IAAI,CAACD,MAAM,CAACjB,SAAS,IAAIU,OAAO,CAAChB,QAAQ,CAAC,KAAK,CAAC,EAAE;QAChDuB,MAAM,CAACjB,SAAS,GAAGU,OAAO,CAACW,SAAS,CAAC,CAAC,CAAC;QACvC;MACF;MACA,IAAI,CAACX,OAAO,CAAChB,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5BuB,MAAM,CAAClB,MAAM,GAAGW,OAAO,CAACW,SAAS,CAAC,CAAC,CAAC;QACpC,IAAID,UAAU,EAAE;UACd9B,eAAM,CAACC,KAAK,CAAE,UAAS6B,UAAW,WAAU,CAAC;UAC7CxB,qBAAY,CAAC0B,GAAG,CAAC1C,IAAI,EAAE,MAAM,EAAEwC,UAAU,CAAC;UAC1CxB,qBAAY,CAAC0B,GAAG,CAAC1C,IAAI,EAAE,eAAe,EAAEqC,MAAM,CAAClB,MAAM,CAAC;QACxD;QACA,OAAOkB,MAAM;MACf;IACF;IACA,OAAOA,MAAM;EACf;;EAEA;EACA,IAAIF,MAAM,KAAK,GAAG,EAAE;IAClBE,MAAM,CAAClB,MAAM,GAAGH,qBAAY,CAACC,GAAG,CAACjB,IAAI,EAAE,eAAe,CAAC,IAAIqC,MAAM,CAAClB,MAAM;EAC1E;;EAEA;EACA,OAAOkB,MAAM;AACf"}