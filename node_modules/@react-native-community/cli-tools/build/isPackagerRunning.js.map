{"version": 3, "names": ["isPackagerRunning", "packagerPort", "process", "env", "RCT_METRO_PORT", "data", "headers", "fetch", "status", "root", "get", "_error"], "sources": ["../src/isPackagerRunning.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {fetch} from './fetch';\n\n/**\n * Indicates whether or not the packager is running. It returns a promise that\n * returns one of these possible values:\n *   - `running`: the packager is running\n *   - `not_running`: the packager nor any process is running on the expected port.\n *   - `unrecognized`: one other process is running on the port we expect the packager to be running.\n */\nasync function isPackagerRunning(\n  packagerPort: string | number = process.env.RCT_METRO_PORT || '8081',\n): Promise<\n  | {\n      status: 'running';\n      root: string;\n    }\n  | 'not_running'\n  | 'unrecognized'\n> {\n  try {\n    const {data, headers} = await fetch(\n      `http://localhost:${packagerPort}/status`,\n    );\n\n    try {\n      if (data === 'packager-status:running') {\n        return {\n          status: 'running',\n          root: headers.get('X-React-Native-Project-Root') ?? '',\n        };\n      }\n    } catch (_error) {\n      return 'unrecognized';\n    }\n\n    return 'unrecognized';\n  } catch (_error) {\n    return 'not_running';\n  }\n}\n\nexport default isPackagerRunning;\n"], "mappings": ";;;;;;AAQA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,iBAAiB,CAC9BC,YAA6B,GAAGC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAI,MAAM,EAQpE;EACA,IAAI;IACF,MAAM;MAACC,IAAI;MAAEC;IAAO,CAAC,GAAG,MAAM,IAAAC,YAAK,EAChC,oBAAmBN,YAAa,SAAQ,CAC1C;IAED,IAAI;MACF,IAAII,IAAI,KAAK,yBAAyB,EAAE;QACtC,OAAO;UACLG,MAAM,EAAE,SAAS;UACjBC,IAAI,EAAEH,OAAO,CAACI,GAAG,CAAC,6BAA6B,CAAC,IAAI;QACtD,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,MAAM,EAAE;MACf,OAAO,cAAc;IACvB;IAEA,OAAO,cAAc;EACvB,CAAC,CAAC,OAAOA,MAAM,EAAE;IACf,OAAO,aAAa;EACtB;AACF;AAAC,eAEcX,iBAAiB;AAAA"}