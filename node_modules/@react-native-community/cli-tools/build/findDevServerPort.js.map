{"version": 3, "names": ["findDevServerPort", "initialPort", "root", "port", "startPackager", "packagerStatus", "isPackagerRunning", "status", "logAlreadyRunningBundler", "result", "handlePortUnavailable", "packager"], "sources": ["../src/findDevServerPort.ts"], "sourcesContent": ["import handlePortUnavailable from './handlePortUnavailable';\nimport isPackagerRunning from './isPackagerRunning';\nimport {logAlreadyRunningBundler} from './port';\n\nconst findDevServerPort = async (\n  initialPort: number,\n  root: string,\n): Promise<{\n  port: number;\n  startPackager: boolean;\n}> => {\n  let port = initialPort;\n  let startPackager = true;\n\n  const packagerStatus = await isPackagerRunning(port);\n\n  if (\n    typeof packagerStatus === 'object' &&\n    packagerStatus.status === 'running'\n  ) {\n    if (packagerStatus.root === root) {\n      startPackager = false;\n      logAlreadyRunningBundler(port);\n    } else {\n      const result = await handlePortUnavailable(port, root);\n      [port, startPackager] = [result.port, result.packager];\n    }\n  } else if (packagerStatus === 'unrecognized') {\n    const result = await handlePortUnavailable(port, root);\n    [port, startPackager] = [result.port, result.packager];\n  }\n\n  return {\n    port,\n    startPackager,\n  };\n};\n\nexport default findDevServerPort;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAAgD;AAEhD,MAAMA,iBAAiB,GAAG,OACxBC,WAAmB,EACnBC,IAAY,KAIR;EACJ,IAAIC,IAAI,GAAGF,WAAW;EACtB,IAAIG,aAAa,GAAG,IAAI;EAExB,MAAMC,cAAc,GAAG,MAAM,IAAAC,0BAAiB,EAACH,IAAI,CAAC;EAEpD,IACE,OAAOE,cAAc,KAAK,QAAQ,IAClCA,cAAc,CAACE,MAAM,KAAK,SAAS,EACnC;IACA,IAAIF,cAAc,CAACH,IAAI,KAAKA,IAAI,EAAE;MAChCE,aAAa,GAAG,KAAK;MACrB,IAAAI,8BAAwB,EAACL,IAAI,CAAC;IAChC,CAAC,MAAM;MACL,MAAMM,MAAM,GAAG,MAAM,IAAAC,8BAAqB,EAACP,IAAI,EAAED,IAAI,CAAC;MACtD,CAACC,IAAI,EAAEC,aAAa,CAAC,GAAG,CAACK,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACE,QAAQ,CAAC;IACxD;EACF,CAAC,MAAM,IAAIN,cAAc,KAAK,cAAc,EAAE;IAC5C,MAAMI,MAAM,GAAG,MAAM,IAAAC,8BAAqB,EAACP,IAAI,EAAED,IAAI,CAAC;IACtD,CAACC,IAAI,EAAEC,aAAa,CAAC,GAAG,CAACK,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACE,QAAQ,CAAC;EACxD;EAEA,OAAO;IACLR,IAAI;IACJC;EACF,CAAC;AACH,CAAC;AAAC,eAEaJ,iBAAiB;AAAA"}