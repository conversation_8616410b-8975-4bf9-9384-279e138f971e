{"version": 3, "names": [], "sources": ["../src/ios.ts"], "sourcesContent": ["/**\n * Types in this document describe the data that is expected by https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/cocoapods/autolinking.rb.\n * When performing changes, make sure to sync it with the Ruby file.\n */\n\nexport interface IOSProjectParams {\n  sourceDir?: string;\n  watchModeCommandParams?: string[];\n  automaticPodsInstallation?: boolean;\n  assets?: string[];\n}\n\nexport type IOSProjectInfo = {\n  name: string;\n  path: string;\n  isWorkspace: boolean;\n};\n\nexport interface IOSProjectConfig {\n  sourceDir: string;\n  xcodeProject: IOSProjectInfo | null;\n  watchModeCommandParams?: string[];\n  automaticPodsInstallation?: boolean;\n  assets: string[];\n}\n\nexport interface IOSDependencyConfig {\n  podspecPath: string;\n  version: string;\n  scriptPhases: Array<IOSScriptPhase>;\n  configurations: string[];\n}\n\nexport type IOSDependencyParams = Omit<\n  Partial<IOSDependencyConfig>,\n  'podspecPath' | 'version'\n>;\n\n/**\n * @see https://www.rubydoc.info/gems/cocoapods-core/Pod/Podfile/DSL#script_phase-instance_method\n *\n * The only difference is that `script` may be omitted in favour of a\n * `path`, relative to the root of the package, whose content will be\n * used.\n */\nexport type IOSScriptPhase = ({script: string} | {path: string}) & {\n  name: string;\n  shell_path?: string;\n  input_files?: string[];\n  output_files?: string[];\n  input_file_lists?: string[];\n  output_file_lists?: string[];\n  show_env_vars_in_log?: boolean;\n  execution_position?: 'before_compile' | 'after_compile' | 'any';\n  dependency_file?: string;\n  always_out_of_date?: string;\n};\n"], "mappings": ""}