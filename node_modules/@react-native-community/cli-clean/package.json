{"name": "@react-native-community/cli-clean", "version": "18.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-tools": "18.0.0", "chalk": "^4.1.2", "execa": "^5.0.0", "fast-glob": "^3.3.2"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "18.0.0", "@types/prompts": "^2.4.4"}, "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-clean", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-clean"}, "gitHead": "f50c1f19a8068787d074560375b726d89c30a088"}