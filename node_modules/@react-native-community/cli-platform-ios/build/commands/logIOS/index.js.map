{"version": 3, "names": ["name", "description", "func", "createLog", "platformName", "options", "getLogOptions"], "sources": ["../../../src/commands/logIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  createLog,\n  getLogOptions,\n} from '@react-native-community/cli-platform-apple';\n\nexport default {\n  name: 'log-ios',\n  description: 'starts iOS device syslog tail',\n  func: createLog({platformName: 'ios'}),\n  options: getLogOptions({platformName: 'ios'}),\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAae;EACbA,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,+BAA+B;EAC5CC,IAAI,EAAE,IAAAC,6BAAS,EAAC;IAACC,YAAY,EAAE;EAAK,CAAC,CAAC;EACtCC,OAAO,EAAE,IAAAC,iCAAa,EAAC;IAACF,YAAY,EAAE;EAAK,CAAC;AAC9C,CAAC;AAAA"}