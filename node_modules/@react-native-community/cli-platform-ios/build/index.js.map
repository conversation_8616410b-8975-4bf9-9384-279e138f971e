{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * iOS platform files\n */\n\nexport {default as commands} from './commands';\n\nexport {\n  findPodfilePaths,\n  getArchitecture,\n  installPods,\n} from '@react-native-community/cli-platform-apple';\n\nexport {dependencyConfig, projectConfig} from './config';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AAAyD"}