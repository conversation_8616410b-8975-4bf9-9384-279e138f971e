{"name": "@react-native-community/cli-platform-ios", "version": "18.0.0", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-platform-apple": "18.0.0"}, "devDependencies": {"hasbin": "^1.2.3"}, "files": ["build", "!*.d.ts", "!*.map"], "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-platform-ios", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-platform-ios"}, "gitHead": "f50c1f19a8068787d074560375b726d89c30a088"}